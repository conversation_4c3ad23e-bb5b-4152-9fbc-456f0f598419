<saveXml type="Object" game4399="true">
  <s type="Object" name="null">
    <s type="Object" name="pet">
      <s type="Number" name="idIndex">0</s>
      <s type="Array" name="arr"/>
      <s type="Object" name="map">
        <s type="Object" name="obj"/>
      </s>
      <s type="Number" name="lockLen">5</s>
      <s type="Object" name="dispatch">
        <s type="Number" name="startTime">0</s>
        <s type="Boolean" name="todayB">false</s>
        <s type="Array" name="idArr"/>
      </s>
    </s>
    <s type="Object" name="gift">
      <s type="Boolean" name="dujia2019_7B">false</s>
      <s type="Object" name="giftGetNumObj"/>
      <s type="Boolean" name="fastCar">false</s>
      <s type="Object" name="summer25">
        <s type="Boolean" name="dailyB">false</s>
        <s type="Number" name="num">0</s>
        <s type="Object" name="getObj"/>
      </s>
      <s type="Boolean" name="isWeekendB">false</s>
      <s type="Number" name="wilderKey">0</s>
      <s type="Boolean" name="chunJie2020B">false</s>
      <s type="Number" name="treasureNum">0</s>
      <s type="Object" name="zhongQiu24">
        <s type="Number" name="cakeNum">0</s>
        <s type="Number" name="loop">0</s>
        <s type="Number" name="useNum">0</s>
      </s>
      <s type="Object" name="dragonChestEnsure">
        <s type="Number" name="allTrigger">0</s>
        <s type="Number" name="dropNum">0</s>
        <s type="Number" name="trigger">0</s>
      </s>
      <s type="Number" name="YXD">0</s>
      <s type="Array" name="ws23"/>
      <s type="Object" name="levelGiftObj"/>
      <s type="Object" name="annSign25">
        <s type="Object" name="eggTO"/>
        <s type="Object" name="eggObj"/>
        <s type="Boolean" name="dailyB">false</s>
        <s type="Number" name="num">0</s>
        <s type="Object" name="getObj">
          <s type="Boolean" name="1">false</s>
        </s>
      </s>
      <s type="Boolean" name="millionpackBestB">false</s>
      <s type="Object" name="anniverGm">
        <s type="Number" name="buyNum">0</s>
        <s type="Number" name="useNum">0</s>
      </s>
      <s type="Boolean" name="darkgoldSuppleB">true</s>
      <s type="Object" name="daily">
        <s type="Object" name="vipGiftGetObj">
          <s type="Boolean" name="00053">true</s>
          <s type="Boolean" name="00050">true</s>
          <s type="Boolean" name="0005000052">true</s>
          <s type="Boolean" name="0004900055">true</s>
          <s type="Boolean" name="00048">true</s>
          <s type="Boolean" name="0004900048">true</s>
        </s>
        <s type="Object" name="giftGetObj">
          <s type="Boolean" name="00053">true</s>
          <s type="Boolean" name="00050">true</s>
          <s type="Boolean" name="0005000052">true</s>
          <s type="Boolean" name="0004900055">true</s>
          <s type="Boolean" name="00048">true</s>
          <s type="Boolean" name="0004900048">true</s>
        </s>
        <s type="Number" name="all">29</s>
        <s type="Array" name="signArr">
          <s type="String" name="null">00050000480005000053000450004800055000450005000057</s>
          <s type="String" name="null">00050000480005000053000450004800055000450004800049</s>
          <s type="String" name="null">00050000480005000053000450004800055000450004800050</s>
          <s type="String" name="null">00050000480005000053000450004800055000450004800051</s>
          <s type="String" name="null">00050000480005000053000450004800055000450004800052</s>
          <s type="String" name="null">00050000480005000053000450004800055000450004800053</s>
          <s type="String" name="null">00050000480005000053000450004800055000450004800054</s>
          <s type="String" name="null">00050000480005000053000450004800055000450004800055</s>
          <s type="String" name="null">00050000480005000053000450004800055000450004800056</s>
          <s type="String" name="null">00050000480005000053000450004800055000450004800057</s>
          <s type="String" name="null">00050000480005000053000450004800055000450004900048</s>
          <s type="String" name="null">00050000480005000053000450004800055000450004900049</s>
          <s type="String" name="null">00050000480005000053000450004800055000450004900050</s>
          <s type="String" name="null">00050000480005000053000450004800055000450004900051</s>
          <s type="String" name="null">00050000480005000053000450004800055000450004900052</s>
          <s type="String" name="null">00050000480005000053000450004800055000450004900053</s>
          <s type="String" name="null">00050000480005000053000450004800055000450004900054</s>
          <s type="String" name="null">00050000480005000053000450004800055000450004900055</s>
          <s type="String" name="null">00050000480005000053000450004800055000450004900056</s>
          <s type="String" name="null">00050000480005000053000450004800055000450004900057</s>
          <s type="String" name="null">00050000480005000053000450004800055000450005000048</s>
          <s type="String" name="null">00050000480005000053000450004800055000450005000049</s>
          <s type="String" name="null">00050000480005000053000450004800055000450005000050</s>
          <s type="String" name="null">00050000480005000053000450004800055000450005000051</s>
          <s type="String" name="null">00050000480005000053000450004800055000450005000052</s>
          <s type="String" name="null">00050000480005000053000450004800055000450005000053</s>
          <s type="String" name="null">00050000480005000053000450004800055000450005000054</s>
          <s type="String" name="null">00050000480005000053000450004800055000450005000055</s>
          <s type="String" name="null">00050000480005000053000450004800055000450005000056</s>
        </s>
      </s>
      <s type="Object" name="guoQing24">
        <s type="Number" name="gn">0</s>
        <s type="Object" name="eggObj"/>
        <s type="Boolean" name="dailyB">false</s>
        <s type="Number" name="num">0</s>
        <s type="Object" name="getObj"/>
        <s type="Number" name="en">0</s>
      </s>
      <s type="Number" name="wsNum23">0</s>
      <s type="Boolean" name="millionpackDayB">false</s>
      <s type="Boolean" name="kaixue2020_1B">false</s>
      <s type="Object" name="cwObj">
        <s type="Object" name="saveObj"/>
      </s>
    </s>
    <s type="Object" name="main">
      <s type="Number" name="zongzi25_num">0</s>
      <s type="Number" name="score">0</s>
      <s type="Array" name="bP1"/>
      <s type="Number" name="memArms">0</s>
      <s type="Number" name="SkillNum2">0</s>
      <s type="Number" name="s18">0</s>
      <s type="Boolean" name="changeNameB">false</s>
      <s type="Number" name="dembs">0</s>
      <s type="Boolean" name="vehicleBackB">false</s>
      <s type="Number" name="maxDp">1470</s>
      <s type="Number" name="daySweeping">0</s>
      <s type="Boolean" name="isZuobiB">false</s>
      <s type="Number" name="swapNum">0</s>
      <s type="Boolean" name="yearArms">false</s>
      <s type="String" name="before"/>
      <s type="Boolean" name="thisYearArms">false</s>
      <s type="Object" name="bookObj"/>
      <s type="Number" name="arms283">0</s>
      <s type="String" name="role">Striker</s>
      <s type="Number" name="dayLottery">2</s>
      <s type="Number" name="goldSpade">0</s>
      <s type="Boolean" name="swapEquipB">false</s>
      <s type="String" name="pass"/>
      <s type="Number" name="bs">0</s>
      <s type="Array" name="armsSkin"/>
      <s type="Number" name="thin">0</s>
      <s type="Number" name="ai">0</s>
      <s type="Array" name="weekArmsArr"/>
      <s type="Number" name="coin">1340929</s>
      <s type="Number" name="versionNumber">34.64</s>
      <s type="Number" name="tenCoin">100</s>
      <s type="Number" name="zongzi25">0</s>
      <s type="Number" name="barren">0</s>
      <s type="String" name="uidMd5">2281ad4fe57ec02e294137ceac53414d</s>
      <s type="Boolean" name="wt27B">false</s>
      <s type="Number" name="anniCoin">104</s>
      <s type="String" name="zuobiReason"/>
      <s type="Number" name="partsC">50</s>
      <s type="Array" name="uP1"/>
      <s type="Number" name="girlSkillNum">0</s>
      <s type="Boolean" name="gift18">false</s>
      <s type="Number" name="delSS">0</s>
      <s type="Number" name="SkillNum1">0</s>
      <s type="Boolean" name="t38">false</s>
      <s type="String" name="ab27">无</s>
    </s>
    <s type="Object" name="worldMap">
      <s type="Object" name="obj">
        <s type="Object" name="XiaSha">
          <s type="Boolean" name="winB">false</s>
          <s type="Number" name="demStone">0</s>
          <s type="Number" name="quitNum">0</s>
          <s type="Object" name="countSave">
            <s type="Number" name="orangeNum">0</s>
            <s type="Number" name="exp">0</s>
            <s type="Number" name="coin">0</s>
            <s type="Number" name="hitNum">0</s>
            <s type="Number" name="star">0</s>
            <s type="Number" name="time">0</s>
            <s type="Number" name="armsNum">0</s>
            <s type="Number" name="geneNum">0</s>
            <s type="Number" name="bulletNum">0</s>
            <s type="Number" name="headshotNum">0</s>
            <s type="Number" name="killEnemyNum">0</s>
            <s type="Number" name="equipNum">0</s>
            <s type="Number" name="rebirthNum">0</s>
          </s>
          <s type="Number" name="winNum">0</s>
          <s type="Number" name="db">0</s>
          <s type="Object" name="timeObj"/>
          <s type="Number" name="dm">0</s>
          <s type="Array" name="challengeNumArr">
            <s type="String" name="null">00048</s>
            <s type="String" name="null">00048</s>
            <s type="String" name="null">00048</s>
            <s type="String" name="null">00048</s>
            <s type="String" name="null">00048</s>
            <s type="String" name="null">00048</s>
            <s type="String" name="null">00048</s>
          </s>
          <s type="Number" name="failNum">0</s>
          <s type="Number" name="maxEndlessScore">0</s>
          <s type="Number" name="demBall">0</s>
          <s type="Number" name="diffUnlock">1</s>
          <s type="Number" name="challengeLevel">0</s>
          <s type="String" name="levelName"/>
          <s type="String" name="name">XiaSha</s>
          <s type="Number" name="maxEndlessGrade">0</s>
          <s type="Number" name="demWin">0</s>
        </s>
        <s type="Object" name="YangMei">
          <s type="Boolean" name="winB">true</s>
          <s type="Number" name="demStone">0</s>
          <s type="Number" name="quitNum">0</s>
          <s type="Object" name="countSave">
            <s type="Number" name="orangeNum">0</s>
            <s type="Number" name="exp">13803</s>
            <s type="Number" name="coin">10</s>
            <s type="Number" name="hitNum">12</s>
            <s type="Number" name="star">4</s>
            <s type="Number" name="time">57.766666666666026</s>
            <s type="Number" name="armsNum">0</s>
            <s type="Number" name="geneNum">0</s>
            <s type="Number" name="bulletNum">17</s>
            <s type="Number" name="headshotNum">7</s>
            <s type="Number" name="killEnemyNum">48</s>
            <s type="Number" name="equipNum">0</s>
            <s type="Number" name="rebirthNum">0</s>
          </s>
          <s type="Number" name="winNum">1</s>
          <s type="Number" name="db">0</s>
          <s type="Object" name="timeObj"/>
          <s type="Number" name="dm">0</s>
          <s type="Array" name="challengeNumArr">
            <s type="String" name="null">00048</s>
            <s type="String" name="null">00048</s>
            <s type="String" name="null">00048</s>
            <s type="String" name="null">00048</s>
            <s type="String" name="null">00048</s>
            <s type="String" name="null">00048</s>
            <s type="String" name="null">00048</s>
          </s>
          <s type="Number" name="failNum">0</s>
          <s type="Number" name="maxEndlessScore">0</s>
          <s type="Number" name="demBall">0</s>
          <s type="Number" name="diffUnlock">1</s>
          <s type="Number" name="challengeLevel">0</s>
          <s type="String" name="levelName">YangMei_2</s>
          <s type="String" name="name">YangMei</s>
          <s type="Number" name="maxEndlessGrade">0</s>
          <s type="Number" name="demWin">0</s>
        </s>
        <s type="Object" name="WoTu">
          <s type="Boolean" name="winB">true</s>
          <s type="Number" name="demStone">0</s>
          <s type="Number" name="quitNum">0</s>
          <s type="Object" name="countSave">
            <s type="Number" name="orangeNum">0</s>
            <s type="Number" name="exp">6104</s>
            <s type="Number" name="coin">77</s>
            <s type="Number" name="hitNum">142</s>
            <s type="Number" name="star">4</s>
            <s type="Number" name="time">74.83333333333174</s>
            <s type="Number" name="armsNum">0</s>
            <s type="Number" name="geneNum">0</s>
            <s type="Number" name="bulletNum">200</s>
            <s type="Number" name="headshotNum">53</s>
            <s type="Number" name="killEnemyNum">72</s>
            <s type="Number" name="equipNum">0</s>
            <s type="Number" name="rebirthNum">0</s>
          </s>
          <s type="Number" name="winNum">1</s>
          <s type="Number" name="db">0</s>
          <s type="Object" name="timeObj"/>
          <s type="Number" name="dm">0</s>
          <s type="Array" name="challengeNumArr">
            <s type="String" name="null">00048</s>
            <s type="String" name="null">00048</s>
            <s type="String" name="null">00048</s>
            <s type="String" name="null">00048</s>
            <s type="String" name="null">00048</s>
            <s type="String" name="null">00048</s>
            <s type="String" name="null">00048</s>
          </s>
          <s type="Number" name="failNum">0</s>
          <s type="Number" name="maxEndlessScore">0</s>
          <s type="Number" name="demBall">0</s>
          <s type="Number" name="diffUnlock">1</s>
          <s type="Number" name="challengeLevel">0</s>
          <s type="String" name="levelName">WoTu_2</s>
          <s type="String" name="name">WoTu</s>
          <s type="Number" name="maxEndlessGrade">0</s>
          <s type="Number" name="demWin">0</s>
        </s>
      </s>
      <s type="Array" name="winMaxDiffMapArr"/>
      <s type="Number" name="ch">0</s>
      <s type="Number" name="sweepingNum">0</s>
      <s type="Boolean" name="dropRocketB">false</s>
      <s type="Number" name="todayEndlessNum">0</s>
      <s type="Number" name="chA">0</s>
      <s type="Number" name="superNum">2</s>
    </s>
    <s type="Object" name="state">
      <s type="Object" name="obj"/>
    </s>
    <s type="Object" name="task">
      <s type="Object" name="todayCompleteNumObj">
        <s type="String" name="week">MA==</s>
      </s>
      <s type="Number" name="swapNum">0</s>
      <s type="Array" name="dataArr"/>
      <s type="Object" name="todayBuyNumObj">
        <s type="String" name="week">MA==</s>
      </s>
      <s type="Number" name="weekMulIndex">0</s>
      <s type="Boolean" name="onlyCompleteB">false</s>
      <s type="Number" name="madN">0</s>
      <s type="String" name="weekDropDateStr"/>
      <s type="Number" name="madG">0</s>
    </s>
    <s type="Object" name="city">
      <s type="Array" name="planArr">
        <s type="String" name="null">镭晶钛晶：周年碎片、78级零件箱</s>
        <s type="String" name="null">镭晶钛晶2：周年碎片、生命药瓶、78级零件箱、72级零件箱、69级零件箱、队友生命药瓶</s>
        <s type="String" name="null">强化剂：强化剂、78级零件箱</s>
        <s type="String" name="null">精石光能石：血石、生命药瓶、队友生命药瓶、弹药箱、78级零件箱</s>
        <s type="String" name="null">强化石神能石：幻想钥匙、勇气钥匙、能量钥匙、胜利钥匙、强化石、转化石</s>
        <s type="String" name="null">远古传奇宝箱：72级零件箱、78级零件箱、稀有宝箱、生命药瓶、队友生命药瓶、弹药箱</s>
        <s type="String" name="null">万能载具碎片：先知碎片、制裁者碎片、泰坦碎片、生命药瓶</s>
        <s type="String" name="null">泰坦碎片：制裁者碎片、先知碎片、万能载具碎片、78级零件箱、75级零件箱</s>
        <s type="String" name="null">副手箱：装置箱、生命药瓶、队友生命药瓶、弹药箱、75级零件箱</s>
      </s>
      <s type="Boolean" name="giftB">false</s>
      <s type="Object" name="dress">
        <s type="Array" name="arr"/>
        <s type="Object" name="space">
          <s type="Object" name="saveObj"/>
        </s>
        <s type="Number" name="idIndex">0</s>
      </s>
      <s type="Number" name="allNum">0</s>
      <s type="Object" name="countObj"/>
      <s type="Number" name="num">0</s>
    </s>
    <s type="Object" name="active">
      <s type="Object" name="haveGiftObj">
        <s type="Boolean" name="active_1">true</s>
      </s>
      <s type="Object" name="acc25_6">
        <s type="Number" name="giftNum">0</s>
        <s type="Boolean" name="dayAddB">false</s>
        <s type="Number" name="day">0</s>
        <s type="Boolean" name="giftB">false</s>
      </s>
    </s>
    <s type="Object" name="skill">
      <s type="Object" name="lockObj"/>
      <s type="Number" name="lockLen">2</s>
      <s type="Number" name="lastId">0</s>
      <s type="Array" name="arr"/>
      <s type="Number" name="gripMaxNum">10</s>
      <s type="Array" name="delNameArr"/>
    </s>
    <s type="Object" name="union">
      <s type="Object" name="conObj"/>
      <s type="String" name="firstTime"/>
      <s type="Number" name="unionId">0</s>
      <s type="Boolean" name="bGiftB">false</s>
      <s type="Number" name="con">0</s>
      <s type="String" name="mp"/>
      <s type="Number" name="removeNum">0</s>
      <s type="Number" name="bNum">0</s>
      <s type="Number" name="bgNum">0</s>
      <s type="Number" name="maxCon">0</s>
      <s type="Object" name="taskObj">
        <s type="Object" name="level">
          <s type="Boolean" name="getB">false</s>
        </s>
        <s type="Object" name="smelt">
          <s type="Boolean" name="getB">false</s>
        </s>
        <s type="Object" name="loveGift">
          <s type="Boolean" name="getB">false</s>
        </s>
        <s type="Object" name="activeValue">
          <s type="Boolean" name="getB">false</s>
        </s>
        <s type="Object" name="wilder">
          <s type="Boolean" name="getB">false</s>
        </s>
        <s type="Object" name="kingTask">
          <s type="Boolean" name="getB">false</s>
        </s>
        <s type="Object" name="federal">
          <s type="Boolean" name="getB">false</s>
        </s>
        <s type="Object" name="arena">
          <s type="Boolean" name="getB">false</s>
        </s>
        <s type="Object" name="extraTask">
          <s type="Boolean" name="getB">false</s>
        </s>
      </s>
      <s type="Number" name="conDay">0</s>
      <s type="Boolean" name="todayGiftB">false</s>
      <s type="String" name="quitT"/>
      <s type="Object" name="building">
        <s type="Number" name="daySuppliesNum">0</s>
        <s type="Boolean" name="federalGiftB">false</s>
        <s type="Object" name="geology">
          <s type="Object" name="thingsObj">
            <s type="Object" name="godStone">
              <s type="Boolean" name="hangingB">true</s>
              <s type="String" name="name">godStone</s>
              <s type="Number" name="now">0</s>
            </s>
            <s type="Object" name="addCoin">
              <s type="Boolean" name="hangingB">true</s>
              <s type="String" name="name">addCoin</s>
              <s type="Number" name="now">0</s>
            </s>
            <s type="Object" name="strengthenStone">
              <s type="Boolean" name="hangingB">true</s>
              <s type="String" name="name">strengthenStone</s>
              <s type="Number" name="now">0</s>
            </s>
            <s type="Object" name="bossCardStamp">
              <s type="Boolean" name="hangingB">true</s>
              <s type="String" name="name">bossCardStamp</s>
              <s type="Number" name="now">0</s>
            </s>
            <s type="Object" name="fineStone">
              <s type="Boolean" name="hangingB">true</s>
              <s type="String" name="name">fineStone</s>
              <s type="Number" name="now">0</s>
            </s>
            <s type="Object" name="skillStone">
              <s type="Boolean" name="hangingB">true</s>
              <s type="String" name="name">skillStone</s>
              <s type="Number" name="now">0</s>
            </s>
            <s type="Object" name="magicChest">
              <s type="Boolean" name="hangingB">true</s>
              <s type="String" name="name">magicChest</s>
              <s type="Number" name="now">0</s>
            </s>
            <s type="Object" name="addLifeMul">
              <s type="Boolean" name="hangingB">true</s>
              <s type="String" name="name">addLifeMul</s>
              <s type="Number" name="now">0</s>
            </s>
            <s type="Object" name="enemy">
              <s type="Boolean" name="hangingB">true</s>
              <s type="String" name="name">enemy</s>
              <s type="Number" name="now">0</s>
            </s>
            <s type="Object" name="converStone">
              <s type="Boolean" name="hangingB">true</s>
              <s type="String" name="name">converStone</s>
              <s type="Number" name="now">0</s>
            </s>
            <s type="Object" name="lightStone">
              <s type="Boolean" name="hangingB">true</s>
              <s type="String" name="name">lightStone</s>
              <s type="Number" name="now">0</s>
            </s>
            <s type="Object" name="bloodStone">
              <s type="Boolean" name="hangingB">true</s>
              <s type="String" name="name">bloodStone</s>
              <s type="Number" name="now">0</s>
            </s>
            <s type="Object" name="normalChest">
              <s type="Boolean" name="hangingB">true</s>
              <s type="String" name="name">normalChest</s>
              <s type="Number" name="now">0</s>
            </s>
          </s>
          <s type="Number" name="haveTime">0</s>
        </s>
        <s type="Number" name="buySuppliesNum">0</s>
        <s type="Number" name="federalTaskNum">0</s>
        <s type="Number" name="eatNum">0</s>
        <s type="Object" name="obj">
          <s type="Object" name="federal">
            <s type="Number" name="exp">0</s>
            <s type="Number" name="lv">1</s>
            <s type="String" name="name">federal</s>
          </s>
          <s type="Object" name="cooking">
            <s type="Number" name="exp">0</s>
            <s type="Number" name="lv">1</s>
            <s type="String" name="name">cooking</s>
          </s>
          <s type="Object" name="geology">
            <s type="Number" name="exp">0</s>
            <s type="Number" name="lv">1</s>
            <s type="String" name="name">geology</s>
          </s>
          <s type="Object" name="watchmen">
            <s type="Number" name="exp">0</s>
            <s type="Number" name="lv">1</s>
            <s type="String" name="name">watchmen</s>
          </s>
        </s>
        <s type="Number" name="sendTaskNum">0</s>
        <s type="Number" name="federalState">0</s>
        <s type="Object" name="cookingObj">
          <s type="Object" name="sandwich">
            <s type="String" name="state">no</s>
            <s type="String" name="name">sandwich</s>
            <s type="Number" name="surplusTime">0</s>
          </s>
          <s type="Object" name="hamburger">
            <s type="String" name="state">no</s>
            <s type="String" name="name">hamburger</s>
            <s type="Number" name="surplusTime">0</s>
          </s>
          <s type="Object" name="slicedBread">
            <s type="String" name="state">no</s>
            <s type="String" name="name">slicedBread</s>
            <s type="Number" name="surplusTime">0</s>
          </s>
        </s>
        <s type="Object" name="sendTaskObj">
          <s type="Object" name="impossibleMission">
            <s type="String" name="state">no</s>
            <s type="Number" name="onHookTime">0</s>
            <s type="Number" name="index">3</s>
            <s type="String" name="name">impossibleMission</s>
            <s type="Number" name="surplusTime">0</s>
          </s>
          <s type="Object" name="grave">
            <s type="String" name="state">no</s>
            <s type="Number" name="onHookTime">0</s>
            <s type="Number" name="index">2</s>
            <s type="String" name="name">grave</s>
            <s type="Number" name="surplusTime">0</s>
          </s>
          <s type="Object" name="farm">
            <s type="String" name="state">no</s>
            <s type="Number" name="onHookTime">0</s>
            <s type="Number" name="index">1</s>
            <s type="String" name="name">farm</s>
            <s type="Number" name="surplusTime">0</s>
          </s>
          <s type="Object" name="prisonOnFire">
            <s type="String" name="state">no</s>
            <s type="Number" name="onHookTime">0</s>
            <s type="Number" name="index">0</s>
            <s type="String" name="name">prisonOnFire</s>
            <s type="Number" name="surplusTime">0</s>
          </s>
        </s>
        <s type="Number" name="exchangeSuppliesNum">0</s>
        <s type="Number" name="haveSendTaskGiftTime">0</s>
      </s>
      <s type="Object" name="donationNumObj"/>
      <s type="Number" name="lt">0</s>
      <s type="Number" name="afCon">0</s>
      <s type="Number" name="beCon">-1</s>
    </s>
    <s type="Object" name="geneBag">
      <s type="Array" name="arr"/>
      <s type="Object" name="lockObj"/>
      <s type="Number" name="lockLen">48</s>
      <s type="Number" name="gripMaxNum">360</s>
      <s type="Number" name="lastId">0</s>
    </s>
    <s type="Object" name="goods">
      <s type="Object" name="buyNumObj"/>
      <s type="Object" name="todayBuyNumObj"/>
      <s type="Number" name="offset2025_7">0</s>
    </s>
    <s type="Object" name="peak">
      <s type="Number" name="dpN">0</s>
      <s type="Object" name="pointObj3">
        <s type="Object" name="saveObj"/>
      </s>
      <s type="String" name="now"/>
      <s type="Object" name="pointObj">
        <s type="Object" name="saveObj"/>
      </s>
      <s type="Number" name="dayExp">0</s>
      <s type="Number" name="exp">0</s>
      <s type="Number" name="lv">1</s>
      <s type="Number" name="dpDay">0</s>
      <s type="Object" name="pointObj2">
        <s type="Object" name="saveObj"/>
      </s>
    </s>
    <s type="Object" name="tower">
      <s type="Number" name="uAll">0</s>
      <s type="Object" name="p1">
        <s type="Object" name="saveObj"/>
      </s>
      <s type="Object" name="dO">
        <s type="Object" name="saveObj"/>
      </s>
      <s type="Number" name="blv">0</s>
      <s type="Object" name="gO">
        <s type="Object" name="saveObj"/>
      </s>
      <s type="Object" name="p0">
        <s type="Object" name="saveObj"/>
      </s>
      <s type="Object" name="p2">
        <s type="Object" name="saveObj"/>
      </s>
      <s type="String" name="pNow">0</s>
      <s type="String" name="ng"/>
      <s type="Boolean" name="ws">false</s>
      <s type="Number" name="unendLv">0</s>
      <s type="Number" name="uP">0</s>
    </s>
    <s type="Object" name="setting">
      <s type="Boolean" name="buff">false</s>
      <s type="Array" name="gene_batchSellColorArr">
        <s type="String" name="null">white</s>
        <s type="String" name="null">green</s>
        <s type="String" name="null">blue</s>
      </s>
      <s type="Boolean" name="trueBossB">false</s>
      <s type="String" name="quality">medium</s>
      <s type="Boolean" name="skillSpecialB">false</s>
      <s type="Boolean" name="critText">false</s>
      <s type="Number" name="frame">30</s>
      <s type="Number" name="ver">0</s>
      <s type="Array" name="gene_batchDecomposeColorArr">
        <s type="String" name="null">white</s>
        <s type="String" name="null">green</s>
        <s type="String" name="null">blue</s>
      </s>
      <s type="Boolean" name="closeWhippB">false</s>
      <s type="Boolean" name="hurtText">false</s>
      <s type="Array" name="arms_batchSellColorArr">
        <s type="String" name="null">white</s>
        <s type="String" name="null">green</s>
        <s type="String" name="null">blue</s>
      </s>
      <s type="Boolean" name="autoSaveB">true</s>
      <s type="String" name="cursor">shoot1</s>
      <s type="Boolean" name="partsSaB">false</s>
      <s type="Array" name="arms_batchDecomposeColorArr">
        <s type="String" name="null">white</s>
        <s type="String" name="null">green</s>
        <s type="String" name="null">blue</s>
        <s type="String" name="null">rare</s>
      </s>
      <s type="Boolean" name="noFloor">true</s>
      <s type="Boolean" name="pauseAffterOutB">true</s>
      <s type="Boolean" name="parnText">true</s>
      <s type="Object" name="armsFilter"/>
      <s type="Boolean" name="allText">false</s>
      <s type="Boolean" name="endlessB">false</s>
      <s type="Boolean" name="simpleNumberB">false</s>
      <s type="Number" name="shootShake">1</s>
      <s type="String" name="loginMu"/>
      <s type="Array" name="equip_batchDecomposeColorArr">
        <s type="String" name="null">white</s>
        <s type="String" name="null">green</s>
        <s type="String" name="null">blue</s>
      </s>
      <s type="Boolean" name="autoLotteryB">false</s>
      <s type="Number" name="sensitivity">0.3</s>
      <s type="Boolean" name="ghostTeB">false</s>
      <s type="Boolean" name="firstChooseB">false</s>
      <s type="String" name="mainMu">lost</s>
      <s type="Boolean" name="fHead">false</s>
      <s type="Number" name="blood">1</s>
      <s type="Boolean" name="echelonB">false</s>
      <s type="Boolean" name="fontLeadB">false</s>
      <s type="Boolean" name="bossLifeB">false</s>
      <s type="Array" name="equip_batchSellColorArr">
        <s type="String" name="null">white</s>
        <s type="String" name="null">green</s>
        <s type="String" name="null">blue</s>
      </s>
      <s type="Boolean" name="spurtingText">false</s>
      <s type="Boolean" name="filterEF">false</s>
      <s type="Number" name="screenShake">0.6</s>
      <s type="Number" name="volume">0.5</s>
      <s type="Object" name="key">
        <s type="Object" name="obj">
          <s type="Object" name="p1">
            <s type="Object" name="arrObj">
              <s type="Array" name="nextArms">
                <s type="Number" name="null">45</s>
              </s>
              <s type="Array" name="skill4">
                <s type="Number" name="null">100</s>
              </s>
              <s type="Array" name="nextSay">
                <s type="Number" name="null">221</s>
              </s>
              <s type="Array" name="skill10">
                <s type="Number" name="null">96</s>
              </s>
              <s type="Array" name="skill2">
                <s type="Number" name="null">98</s>
              </s>
              <s type="Array" name="skill8">
                <s type="Number" name="null">104</s>
              </s>
              <s type="Array" name="attack">
                <s type="Number" name="null">33</s>
              </s>
              <s type="Array" name="spaceToken">
                <s type="Number" name="null">71</s>
              </s>
              <s type="Array" name="left">
                <s type="Number" name="null">46</s>
              </s>
              <s type="Array" name="levelCount">
                <s type="Number" name="null">80</s>
              </s>
              <s type="Array" name="airLand"/>
              <s type="Array" name="skill6">
                <s type="Number" name="null">102</s>
              </s>
              <s type="Array" name="skill1">
                <s type="Number" name="null">97</s>
              </s>
              <s type="Array" name="right">
                <s type="Number" name="null">34</s>
              </s>
              <s type="Array" name="skill3">
                <s type="Number" name="null">99</s>
              </s>
              <s type="Array" name="ai">
                <s type="Number" name="null">78</s>
              </s>
              <s type="Array" name="caisson">
                <s type="Number" name="null">67</s>
              </s>
              <s type="Array" name="shoot">
                <s type="Number" name="null">-1</s>
              </s>
              <s type="Array" name="teamLifeBottle">
                <s type="Number" name="null">88</s>
              </s>
              <s type="Array" name="skill7">
                <s type="Number" name="null">103</s>
              </s>
              <s type="Array" name="teamRebirthCard">
                <s type="Number" name="null">86</s>
              </s>
              <s type="Array" name="lifeBottle">
                <s type="Number" name="null">38</s>
              </s>
              <s type="Array" name="changeCharger">
                <s type="Number" name="null">220</s>
              </s>
              <s type="Array" name="skillFleshCard">
                <s type="Number" name="null">66</s>
              </s>
              <s type="Array" name="skill9">
                <s type="Number" name="null">105</s>
              </s>
              <s type="Array" name="up">
                <s type="Number" name="null">36</s>
              </s>
              <s type="Array" name="down">
                <s type="Number" name="null">35</s>
              </s>
              <s type="Array" name="weapon">
                <s type="Number" name="null">39</s>
              </s>
              <s type="Array" name="skill5">
                <s type="Number" name="null">101</s>
              </s>
              <s type="Array" name="bossSumCard">
                <s type="Number" name="null">72</s>
              </s>
            </s>
            <s type="String" name="type">p1</s>
          </s>
          <s type="Object" name="p2">
            <s type="Object" name="arrObj">
              <s type="Array" name="nextArms">
                <s type="Number" name="null">81</s>
              </s>
              <s type="Array" name="skill4">
                <s type="Number" name="null">52</s>
              </s>
              <s type="Array" name="nextSay">
                <s type="Number" name="null">70</s>
              </s>
              <s type="Array" name="skill10">
                <s type="Number" name="null">48</s>
              </s>
              <s type="Array" name="skill2">
                <s type="Number" name="null">50</s>
              </s>
              <s type="Array" name="skill8">
                <s type="Number" name="null">56</s>
              </s>
              <s type="Array" name="attack">
                <s type="Number" name="null">69</s>
              </s>
              <s type="Array" name="spaceToken"/>
              <s type="Array" name="left">
                <s type="Number" name="null">65</s>
              </s>
              <s type="Array" name="levelCount"/>
              <s type="Array" name="airLand">
                <s type="Number" name="null">73</s>
              </s>
              <s type="Array" name="skill6">
                <s type="Number" name="null">54</s>
              </s>
              <s type="Array" name="skill1">
                <s type="Number" name="null">49</s>
              </s>
              <s type="Array" name="right">
                <s type="Number" name="null">68</s>
              </s>
              <s type="Array" name="skill3">
                <s type="Number" name="null">51</s>
              </s>
              <s type="Array" name="ai"/>
              <s type="Array" name="caisson"/>
              <s type="Array" name="shoot">
                <s type="Number" name="null">74</s>
              </s>
              <s type="Array" name="teamLifeBottle"/>
              <s type="Array" name="skill7">
                <s type="Number" name="null">55</s>
              </s>
              <s type="Array" name="teamRebirthCard"/>
              <s type="Array" name="lifeBottle"/>
              <s type="Array" name="changeCharger">
                <s type="Number" name="null">82</s>
              </s>
              <s type="Array" name="skillFleshCard"/>
              <s type="Array" name="skill9">
                <s type="Number" name="null">57</s>
              </s>
              <s type="Array" name="up">
                <s type="Number" name="null">87</s>
              </s>
              <s type="Array" name="down">
                <s type="Number" name="null">83</s>
              </s>
              <s type="Array" name="weapon">
                <s type="Number" name="null">72</s>
              </s>
              <s type="Array" name="skill5">
                <s type="Number" name="null">53</s>
              </s>
              <s type="Array" name="bossSumCard"/>
            </s>
            <s type="String" name="type">p2</s>
          </s>
          <s type="Object" name="single">
            <s type="Object" name="arrObj">
              <s type="Array" name="nextArms">
                <s type="Number" name="null">81</s>
              </s>
              <s type="Array" name="skill4">
                <s type="Number" name="null">52</s>
              </s>
              <s type="Array" name="nextSay">
                <s type="Number" name="null">70</s>
              </s>
              <s type="Array" name="skill10">
                <s type="Number" name="null">48</s>
              </s>
              <s type="Array" name="skill2">
                <s type="Number" name="null">50</s>
              </s>
              <s type="Array" name="skill8">
                <s type="Number" name="null">56</s>
              </s>
              <s type="Array" name="attack">
                <s type="Number" name="null">69</s>
              </s>
              <s type="Array" name="spaceToken">
                <s type="Number" name="null">71</s>
              </s>
              <s type="Array" name="left">
                <s type="Number" name="null">65</s>
              </s>
              <s type="Array" name="levelCount">
                <s type="Number" name="null">80</s>
              </s>
              <s type="Array" name="airLand">
                <s type="Number" name="null">73</s>
              </s>
              <s type="Array" name="skill6">
                <s type="Number" name="null">54</s>
              </s>
              <s type="Array" name="skill1">
                <s type="Number" name="null">49</s>
              </s>
              <s type="Array" name="right">
                <s type="Number" name="null">68</s>
              </s>
              <s type="Array" name="skill3">
                <s type="Number" name="null">51</s>
              </s>
              <s type="Array" name="ai">
                <s type="Number" name="null">78</s>
              </s>
              <s type="Array" name="caisson">
                <s type="Number" name="null">67</s>
              </s>
              <s type="Array" name="shoot">
                <s type="Number" name="null">-1</s>
              </s>
              <s type="Array" name="teamLifeBottle">
                <s type="Number" name="null">88</s>
              </s>
              <s type="Array" name="skill7">
                <s type="Number" name="null">55</s>
              </s>
              <s type="Array" name="teamRebirthCard">
                <s type="Number" name="null">86</s>
              </s>
              <s type="Array" name="lifeBottle">
                <s type="Number" name="null">90</s>
              </s>
              <s type="Array" name="changeCharger">
                <s type="Number" name="null">82</s>
              </s>
              <s type="Array" name="skillFleshCard">
                <s type="Number" name="null">66</s>
              </s>
              <s type="Array" name="skill9">
                <s type="Number" name="null">57</s>
              </s>
              <s type="Array" name="up">
                <s type="Number" name="null">87</s>
              </s>
              <s type="Array" name="down">
                <s type="Number" name="null">83</s>
              </s>
              <s type="Array" name="weapon">
                <s type="Number" name="null">72</s>
              </s>
              <s type="Array" name="skill5">
                <s type="Number" name="null">53</s>
              </s>
              <s type="Array" name="bossSumCard">
                <s type="Number" name="null">72</s>
              </s>
            </s>
            <s type="String" name="type">single</s>
          </s>
        </s>
      </s>
      <s type="Number" name="partsComposeMaxLv">69</s>
    </s>
    <s type="Object" name="bossEdit">
      <s type="Object" name="collect">
        <s type="Number" name="lastId">0</s>
        <s type="Array" name="arr"/>
      </s>
      <s type="Number" name="lastId">0</s>
      <s type="Number" name="bA">0</s>
      <s type="Number" name="upNum">0</s>
      <s type="Object" name="history">
        <s type="Number" name="lastId">0</s>
        <s type="Array" name="arr"/>
      </s>
      <s type="Object" name="diffO"/>
      <s type="String" name="mainId"/>
      <s type="Array" name="arr"/>
      <s type="Object" name="taskO"/>
      <s type="Number" name="mBA">0</s>
      <s type="Number" name="bs">0</s>
    </s>
    <s type="Object" name="armsBag">
      <s type="Array" name="arr">
        <s type="Object" name="null">
          <s type="Number" name="capacity">6</s>
          <s type="Object" name="critD">
            <s type="Number" name="mul">0</s>
            <s type="Number" name="pro">0</s>
          </s>
          <s type="Number" name="reloadGap">2.074272050289437</s>
          <s type="Number" name="penetrationNum">0</s>
          <s type="String" name="cnName">末日之刃</s>
          <s type="Array" name="l"/>
          <s type="Boolean" name="firstChoiceB">false</s>
          <s type="Object" name="followD">
            <s type="Number" name="value">0</s>
            <s type="Number" name="maxTime">10000</s>
            <s type="Boolean" name="hitIsTargetB">false</s>
            <s type="Boolean" name="noLM">false</s>
            <s type="Number" name="delay">0</s>
          </s>
          <s type="Number" name="strengthenLv">0</s>
          <s type="Number" name="addLevel">0</s>
          <s type="Number" name="shootAngle">0</s>
          <s type="String" name="id">0102_05_995_00000001</s>
          <s type="Object" name="o"/>
          <s type="Number" name="bulletNum">1</s>
          <s type="Number" name="hurtRatio">222</s>
          <s type="Number" name="eleLv">0</s>
          <s type="Number" name="itemsLevel">4</s>
          <s type="Number" name="bh">0</s>
          <s type="String" name="name">sniperRifle</s>
          <s type="Boolean" name="upgradeB">true</s>
          <s type="Object" name="bounceD">
            <s type="Boolean" name="noDieB">false</s>
            <s type="String" name="shakeString"/>
            <s type="Number" name="vMul">1</s>
            <s type="Number" name="floor">0</s>
            <s type="Boolean" name="glueFloorB">false</s>
            <s type="Number" name="body">0</s>
            <s type="Number" name="hurtNumAdd">0</s>
            <s type="Number" name="noHitTime">0</s>
            <s type="Boolean" name="liveInitB">false</s>
          </s>
          <s type="Number" name="twoShootPro">0.10324073783122004</s>
          <s type="String" name="itemsType">arms</s>
          <s type="String" name="s"/>
          <s type="Boolean" name="shopB">false</s>
          <s type="Number" name="sMaxLv">0</s>
          <s type="String" name="armsImgLabel">texture$t16_shotgun2$body_sniper3$barrel_sniper1$grip_sniper1$bullet_shotgun1$stock_sniper3$glass</s>
          <s type="Number" name="strengthenNum">0</s>
          <s type="Number" name="evoLv">1</s>
          <s type="Number" name="bulletWidth">1006</s>
          <s type="String" name="shootSoundUrl">sniper3/barrel_sound</s>
          <s type="Boolean" name="lockB">false</s>
          <s type="Boolean" name="newB">false</s>
          <s type="String" name="color">purple</s>
          <s type="String" name="getTime">2025-07-29 14:31:48</s>
          <s type="Number" name="penetrationGap">0</s>
          <s type="Number" name="shakeAngle">0.586186436470598</s>
          <s type="String" name="severTime">2025-07-29 14:29:42</s>
          <s type="String" name="inHouseTime"/>
          <s type="String" name="ele"/>
          <s type="Number" name="attackGap">1.7527541367337107</s>
          <s type="Array" name="skillArr">
            <s type="String" name="null">Hit_SlowMove_ArmsSkill</s>
          </s>
          <s type="Number" name="bulletShakeWidth">38</s>
          <s type="Number" name="site">2</s>
          <s type="Array" name="godSkillArr"/>
          <s type="Object" name="partsSave">
            <s type="Array" name="arr"/>
            <s type="Object" name="lockObj"/>
            <s type="Number" name="lockLen">10</s>
            <s type="Number" name="gripMaxNum">15</s>
            <s type="Number" name="lastId">0</s>
          </s>
        </s>
        <s type="Object" name="null">
          <s type="Number" name="capacity">6</s>
          <s type="Object" name="critD">
            <s type="Number" name="mul">0</s>
            <s type="Number" name="pro">0</s>
          </s>
          <s type="Number" name="reloadGap">2.4062531711533666</s>
          <s type="Number" name="penetrationNum">0</s>
          <s type="String" name="cnName">狂热轰炸者</s>
          <s type="Array" name="l"/>
          <s type="Boolean" name="firstChoiceB">false</s>
          <s type="Object" name="followD">
            <s type="Number" name="value">0</s>
            <s type="Number" name="maxTime">10000</s>
            <s type="Boolean" name="hitIsTargetB">false</s>
            <s type="Boolean" name="noLM">false</s>
            <s type="Number" name="delay">0</s>
          </s>
          <s type="Number" name="strengthenLv">0</s>
          <s type="Number" name="addLevel">0</s>
          <s type="Number" name="shootAngle">0</s>
          <s type="String" name="id">0105_06_995_00000004</s>
          <s type="Object" name="o"/>
          <s type="Number" name="bulletNum">1</s>
          <s type="Number" name="hurtRatio">52</s>
          <s type="Number" name="eleLv">0</s>
          <s type="Number" name="itemsLevel">4</s>
          <s type="Number" name="bh">0</s>
          <s type="String" name="name">rocket1</s>
          <s type="Boolean" name="upgradeB">true</s>
          <s type="Object" name="bounceD">
            <s type="Boolean" name="noDieB">false</s>
            <s type="String" name="shakeString"/>
            <s type="Number" name="vMul">1</s>
            <s type="Number" name="floor">0</s>
            <s type="Boolean" name="glueFloorB">false</s>
            <s type="Number" name="body">0</s>
            <s type="Number" name="hurtNumAdd">0</s>
            <s type="Number" name="noHitTime">0</s>
            <s type="Boolean" name="liveInitB">false</s>
          </s>
          <s type="Number" name="twoShootPro">0</s>
          <s type="String" name="itemsType">arms</s>
          <s type="String" name="s"/>
          <s type="Boolean" name="shopB">false</s>
          <s type="Number" name="sMaxLv">0</s>
          <s type="String" name="armsImgLabel">texture$m24_rocket$body_rocket$barrel_pistol3$grip_rocket$bullet_rocket$stock_0</s>
          <s type="Number" name="strengthenNum">0</s>
          <s type="Number" name="evoLv">1</s>
          <s type="Number" name="bulletWidth">25</s>
          <s type="String" name="shootSoundUrl">rocket/barrel_sound</s>
          <s type="Boolean" name="lockB">false</s>
          <s type="Boolean" name="newB">false</s>
          <s type="String" name="color">blue</s>
          <s type="String" name="getTime">2025-07-29 14:31:48</s>
          <s type="Number" name="penetrationGap">0</s>
          <s type="Number" name="shakeAngle">0.5303254169411957</s>
          <s type="String" name="severTime">2025-07-29 14:29:42</s>
          <s type="String" name="inHouseTime"/>
          <s type="String" name="ele"/>
          <s type="Number" name="attackGap">0.8783931452315301</s>
          <s type="Array" name="skillArr">
            <s type="String" name="null">Kill_AddCharger_ArmsSkill</s>
          </s>
          <s type="Number" name="bulletShakeWidth">0</s>
          <s type="Number" name="site">1</s>
          <s type="Array" name="godSkillArr"/>
          <s type="Object" name="partsSave">
            <s type="Array" name="arr"/>
            <s type="Object" name="lockObj"/>
            <s type="Number" name="lockLen">10</s>
            <s type="Number" name="gripMaxNum">15</s>
            <s type="Number" name="lastId">0</s>
          </s>
        </s>
        <s type="Object" name="null">
          <s type="Number" name="capacity">6</s>
          <s type="Object" name="critD">
            <s type="Number" name="mul">0</s>
            <s type="Number" name="pro">0</s>
          </s>
          <s type="Number" name="reloadGap">2.384363074088469</s>
          <s type="Number" name="penetrationNum">0</s>
          <s type="String" name="cnName">怒气轰炸者</s>
          <s type="Array" name="l"/>
          <s type="Boolean" name="firstChoiceB">false</s>
          <s type="Object" name="followD">
            <s type="Number" name="value">0</s>
            <s type="Number" name="maxTime">10000</s>
            <s type="Boolean" name="hitIsTargetB">false</s>
            <s type="Boolean" name="noLM">false</s>
            <s type="Number" name="delay">0</s>
          </s>
          <s type="Number" name="strengthenLv">0</s>
          <s type="Number" name="addLevel">0</s>
          <s type="Number" name="shootAngle">0</s>
          <s type="String" name="id">0105_06_995_00000006</s>
          <s type="Object" name="o"/>
          <s type="Number" name="bulletNum">1</s>
          <s type="Number" name="hurtRatio">49</s>
          <s type="Number" name="eleLv">0</s>
          <s type="Number" name="itemsLevel">4</s>
          <s type="Number" name="bh">0</s>
          <s type="String" name="name">rocket1</s>
          <s type="Boolean" name="upgradeB">true</s>
          <s type="Object" name="bounceD">
            <s type="Boolean" name="noDieB">false</s>
            <s type="String" name="shakeString"/>
            <s type="Number" name="vMul">1</s>
            <s type="Number" name="floor">0</s>
            <s type="Boolean" name="glueFloorB">false</s>
            <s type="Number" name="body">0</s>
            <s type="Number" name="hurtNumAdd">0</s>
            <s type="Number" name="noHitTime">0</s>
            <s type="Boolean" name="liveInitB">false</s>
          </s>
          <s type="Number" name="twoShootPro">0</s>
          <s type="String" name="itemsType">arms</s>
          <s type="String" name="s"/>
          <s type="Boolean" name="shopB">false</s>
          <s type="Number" name="sMaxLv">0</s>
          <s type="String" name="armsImgLabel">texture$m19_rocket$body2_rocket$barrel_sniper2$grip_rocket$bullet_rocket$stock2_0</s>
          <s type="Number" name="strengthenNum">0</s>
          <s type="Number" name="evoLv">1</s>
          <s type="Number" name="bulletWidth">25</s>
          <s type="String" name="shootSoundUrl">rocket/barrel_sound</s>
          <s type="Boolean" name="lockB">false</s>
          <s type="Boolean" name="newB">false</s>
          <s type="String" name="color">blue</s>
          <s type="String" name="getTime">2025-07-29 14:31:48</s>
          <s type="Number" name="penetrationGap">0</s>
          <s type="Number" name="shakeAngle">0.5944691016338766</s>
          <s type="String" name="severTime">2025-07-29 14:29:42</s>
          <s type="String" name="inHouseTime"/>
          <s type="String" name="ele"/>
          <s type="Number" name="attackGap">0.8043792328797281</s>
          <s type="Array" name="skillArr">
            <s type="String" name="null">Kill_Crazy_ArmsSkill</s>
          </s>
          <s type="Number" name="bulletShakeWidth">0</s>
          <s type="Number" name="site">0</s>
          <s type="Array" name="godSkillArr"/>
          <s type="Object" name="partsSave">
            <s type="Array" name="arr"/>
            <s type="Object" name="lockObj"/>
            <s type="Number" name="lockLen">10</s>
            <s type="Number" name="gripMaxNum">15</s>
            <s type="Number" name="lastId">0</s>
          </s>
        </s>
        <s type="Object" name="null">
          <s type="Number" name="capacity">41</s>
          <s type="Object" name="critD">
            <s type="Number" name="mul">0</s>
            <s type="Number" name="pro">0</s>
          </s>
          <s type="Number" name="reloadGap">1.9761052071116865</s>
          <s type="Number" name="penetrationNum">1</s>
          <s type="String" name="cnName">破空弑神</s>
          <s type="Array" name="l"/>
          <s type="Boolean" name="firstChoiceB">false</s>
          <s type="Object" name="followD">
            <s type="Number" name="value">0</s>
            <s type="Number" name="maxTime">0</s>
            <s type="Boolean" name="hitIsTargetB">false</s>
            <s type="Boolean" name="noLM">false</s>
            <s type="Number" name="delay">0</s>
          </s>
          <s type="Number" name="strengthenLv">0</s>
          <s type="Number" name="addLevel">0</s>
          <s type="Number" name="shootAngle">0</s>
          <s type="String" name="id">0101_05_994_00000028</s>
          <s type="Object" name="o"/>
          <s type="Number" name="bulletNum">1</s>
          <s type="Number" name="hurtRatio">21</s>
          <s type="Number" name="eleLv">0</s>
          <s type="Number" name="itemsLevel">5</s>
          <s type="Number" name="bh">0</s>
          <s type="String" name="name">ak47</s>
          <s type="Boolean" name="upgradeB">true</s>
          <s type="Object" name="bounceD">
            <s type="Boolean" name="noDieB">false</s>
            <s type="String" name="shakeString"/>
            <s type="Number" name="vMul">1</s>
            <s type="Number" name="floor">0</s>
            <s type="Boolean" name="glueFloorB">false</s>
            <s type="Number" name="body">0</s>
            <s type="Number" name="hurtNumAdd">0</s>
            <s type="Number" name="noHitTime">0</s>
            <s type="Boolean" name="liveInitB">false</s>
          </s>
          <s type="Number" name="twoShootPro">0</s>
          <s type="String" name="itemsType">arms</s>
          <s type="String" name="s"/>
          <s type="Boolean" name="shopB">false</s>
          <s type="Number" name="sMaxLv">0</s>
          <s type="String" name="armsImgLabel">texture$t7_shotgun3$body_ak$barrel7_xm8$grip_ak$bullet_m4$stock_0</s>
          <s type="Number" name="strengthenNum">0</s>
          <s type="Number" name="evoLv">1</s>
          <s type="Number" name="bulletWidth">621</s>
          <s type="String" name="shootSoundUrl">ak/barrel7_sound</s>
          <s type="Boolean" name="lockB">false</s>
          <s type="Boolean" name="newB">false</s>
          <s type="String" name="color">purple</s>
          <s type="String" name="getTime">2025-07-29 14:31:48</s>
          <s type="Number" name="penetrationGap">0</s>
          <s type="Number" name="shakeAngle">4.721888990141451</s>
          <s type="String" name="severTime">2025-07-29 14:29:42</s>
          <s type="String" name="inHouseTime"/>
          <s type="String" name="ele"/>
          <s type="Number" name="attackGap">0.1198777630040422</s>
          <s type="Array" name="skillArr">
            <s type="String" name="null">Hit_SlowMove_ArmsSkill</s>
          </s>
          <s type="Number" name="bulletShakeWidth">73</s>
          <s type="Number" name="site">3</s>
          <s type="Array" name="godSkillArr"/>
          <s type="Object" name="partsSave">
            <s type="Array" name="arr"/>
            <s type="Object" name="lockObj"/>
            <s type="Number" name="lockLen">10</s>
            <s type="Number" name="gripMaxNum">15</s>
            <s type="Number" name="lastId">0</s>
          </s>
        </s>
        <s type="Object" name="null">
          <s type="Number" name="capacity">35</s>
          <s type="Object" name="critD">
            <s type="Number" name="mul">0</s>
            <s type="Number" name="pro">0</s>
          </s>
          <s type="Number" name="reloadGap">1.980475266929716</s>
          <s type="Number" name="penetrationNum">0</s>
          <s type="String" name="cnName">灭亡入侵者</s>
          <s type="Array" name="l"/>
          <s type="Boolean" name="firstChoiceB">false</s>
          <s type="Object" name="followD">
            <s type="Number" name="value">0</s>
            <s type="Number" name="maxTime">0</s>
            <s type="Boolean" name="hitIsTargetB">false</s>
            <s type="Boolean" name="noLM">false</s>
            <s type="Number" name="delay">0</s>
          </s>
          <s type="Number" name="strengthenLv">0</s>
          <s type="Number" name="addLevel">0</s>
          <s type="Number" name="shootAngle">0</s>
          <s type="String" name="id">0101_05_995_00000029</s>
          <s type="Object" name="o"/>
          <s type="Number" name="bulletNum">1</s>
          <s type="Number" name="hurtRatio">14</s>
          <s type="Number" name="eleLv">0</s>
          <s type="Number" name="itemsLevel">4</s>
          <s type="Number" name="bh">0</s>
          <s type="String" name="name">ak47</s>
          <s type="Boolean" name="upgradeB">true</s>
          <s type="Object" name="bounceD">
            <s type="Boolean" name="noDieB">false</s>
            <s type="String" name="shakeString"/>
            <s type="Number" name="vMul">1</s>
            <s type="Number" name="floor">0</s>
            <s type="Boolean" name="glueFloorB">false</s>
            <s type="Number" name="body">1</s>
            <s type="Number" name="hurtNumAdd">0</s>
            <s type="Number" name="noHitTime">0</s>
            <s type="Boolean" name="liveInitB">false</s>
          </s>
          <s type="Number" name="twoShootPro">0</s>
          <s type="String" name="itemsType">arms</s>
          <s type="String" name="s"/>
          <s type="Boolean" name="shopB">false</s>
          <s type="Number" name="sMaxLv">0</s>
          <s type="String" name="armsImgLabel">texture$t17_ak$body_ak$barrel6_m4$grip_xm8$bullet_sniper2$stock_0</s>
          <s type="Number" name="strengthenNum">0</s>
          <s type="Number" name="evoLv">1</s>
          <s type="Number" name="bulletWidth">501</s>
          <s type="String" name="shootSoundUrl">ak/barrel6_sound</s>
          <s type="Boolean" name="lockB">false</s>
          <s type="Boolean" name="newB">false</s>
          <s type="String" name="color">purple</s>
          <s type="String" name="getTime">2025-07-29 14:31:49</s>
          <s type="Number" name="penetrationGap">0</s>
          <s type="Number" name="shakeAngle">7.4156158016994596</s>
          <s type="String" name="severTime">2025-07-29 14:29:42</s>
          <s type="String" name="inHouseTime"/>
          <s type="String" name="ele"/>
          <s type="Number" name="attackGap">0.1042014279216528</s>
          <s type="Array" name="skillArr">
            <s type="String" name="null">Hit_silence_ArmsSkill_rifle</s>
          </s>
          <s type="Number" name="bulletShakeWidth">76</s>
          <s type="Number" name="site">6</s>
          <s type="Array" name="godSkillArr"/>
          <s type="Object" name="partsSave">
            <s type="Array" name="arr"/>
            <s type="Object" name="lockObj"/>
            <s type="Number" name="lockLen">10</s>
            <s type="Number" name="gripMaxNum">15</s>
            <s type="Number" name="lastId">0</s>
          </s>
        </s>
        <s type="Object" name="null">
          <s type="Number" name="capacity">6</s>
          <s type="Object" name="critD">
            <s type="Number" name="mul">0</s>
            <s type="Number" name="pro">0</s>
          </s>
          <s type="Number" name="reloadGap">1.7501710262149572</s>
          <s type="Number" name="penetrationNum">0</s>
          <s type="String" name="cnName">猎隐制裁者</s>
          <s type="Array" name="l"/>
          <s type="Boolean" name="firstChoiceB">false</s>
          <s type="Object" name="followD">
            <s type="Number" name="value">0</s>
            <s type="Number" name="maxTime">0</s>
            <s type="Boolean" name="hitIsTargetB">false</s>
            <s type="Boolean" name="noLM">false</s>
            <s type="Number" name="delay">0</s>
          </s>
          <s type="Number" name="strengthenLv">0</s>
          <s type="Number" name="addLevel">0</s>
          <s type="Number" name="shootAngle">19.79545848025009</s>
          <s type="String" name="id">0103_05_994_00000040</s>
          <s type="Object" name="o"/>
          <s type="Number" name="bulletNum">6</s>
          <s type="Number" name="hurtRatio">68</s>
          <s type="Number" name="eleLv">0</s>
          <s type="Number" name="itemsLevel">5</s>
          <s type="Number" name="bh">0</s>
          <s type="String" name="name">shotgun1</s>
          <s type="Boolean" name="upgradeB">true</s>
          <s type="Object" name="bounceD">
            <s type="Boolean" name="noDieB">false</s>
            <s type="String" name="shakeString"/>
            <s type="Number" name="vMul">1</s>
            <s type="Number" name="floor">0</s>
            <s type="Boolean" name="glueFloorB">false</s>
            <s type="Number" name="body">1</s>
            <s type="Number" name="hurtNumAdd">0</s>
            <s type="Number" name="noHitTime">0</s>
            <s type="Boolean" name="liveInitB">false</s>
          </s>
          <s type="Number" name="twoShootPro">0</s>
          <s type="String" name="itemsType">arms</s>
          <s type="String" name="s"/>
          <s type="Boolean" name="shopB">false</s>
          <s type="Number" name="sMaxLv">0</s>
          <s type="String" name="armsImgLabel">texture$t10_shotgun2$body_shotgun1$barrel_shotgun1$grip_shotgun1$bullet_shotgun2$stock_0</s>
          <s type="Number" name="strengthenNum">0</s>
          <s type="Number" name="evoLv">1</s>
          <s type="Number" name="bulletWidth">565</s>
          <s type="String" name="shootSoundUrl">shotgun1/barrel_sound</s>
          <s type="Boolean" name="lockB">false</s>
          <s type="Boolean" name="newB">false</s>
          <s type="String" name="color">purple</s>
          <s type="String" name="getTime">2025-07-29 14:31:49</s>
          <s type="Number" name="penetrationGap">0</s>
          <s type="Number" name="shakeAngle">0.9500675327144564</s>
          <s type="String" name="severTime">2025-07-29 14:29:42</s>
          <s type="String" name="inHouseTime"/>
          <s type="String" name="ele"/>
          <s type="Number" name="attackGap">0.963171435892582</s>
          <s type="Array" name="skillArr">
            <s type="String" name="null">Hit_Spurting_ArmsSkill</s>
          </s>
          <s type="Number" name="bulletShakeWidth">69</s>
          <s type="Number" name="site">4</s>
          <s type="Array" name="godSkillArr"/>
          <s type="Object" name="partsSave">
            <s type="Array" name="arr"/>
            <s type="Object" name="lockObj"/>
            <s type="Number" name="lockLen">10</s>
            <s type="Number" name="gripMaxNum">15</s>
            <s type="Number" name="lastId">0</s>
          </s>
        </s>
        <s type="Object" name="null">
          <s type="Number" name="capacity">16</s>
          <s type="Object" name="critD">
            <s type="Number" name="mul">0</s>
            <s type="Number" name="pro">0</s>
          </s>
          <s type="Number" name="reloadGap">2.057338963728398</s>
          <s type="Number" name="penetrationNum">0</s>
          <s type="String" name="cnName">无限之伤</s>
          <s type="Array" name="l"/>
          <s type="Boolean" name="firstChoiceB">false</s>
          <s type="Object" name="followD">
            <s type="Number" name="value">0</s>
            <s type="Number" name="maxTime">0</s>
            <s type="Boolean" name="hitIsTargetB">false</s>
            <s type="Boolean" name="noLM">false</s>
            <s type="Number" name="delay">0</s>
          </s>
          <s type="Number" name="strengthenLv">0</s>
          <s type="Number" name="addLevel">0</s>
          <s type="Number" name="shootAngle">0</s>
          <s type="String" name="id">0104_05_994_00000041</s>
          <s type="Object" name="o"/>
          <s type="Number" name="bulletNum">1</s>
          <s type="Number" name="hurtRatio">50</s>
          <s type="Number" name="eleLv">0</s>
          <s type="Number" name="itemsLevel">5</s>
          <s type="Number" name="bh">0</s>
          <s type="String" name="name">pistol1</s>
          <s type="Boolean" name="upgradeB">true</s>
          <s type="Object" name="bounceD">
            <s type="Boolean" name="noDieB">false</s>
            <s type="String" name="shakeString"/>
            <s type="Number" name="vMul">1</s>
            <s type="Number" name="floor">1</s>
            <s type="Boolean" name="glueFloorB">false</s>
            <s type="Number" name="body">0</s>
            <s type="Number" name="hurtNumAdd">0</s>
            <s type="Number" name="noHitTime">0</s>
            <s type="Boolean" name="liveInitB">false</s>
          </s>
          <s type="Number" name="twoShootPro">0</s>
          <s type="String" name="itemsType">arms</s>
          <s type="String" name="s"/>
          <s type="Boolean" name="shopB">false</s>
          <s type="Number" name="sMaxLv">0</s>
          <s type="String" name="armsImgLabel">texture$t9_pistol1$body_pistol1$barrel4_sniper2$grip_pistol2$bullet_0_0</s>
          <s type="Number" name="strengthenNum">0</s>
          <s type="Number" name="evoLv">1</s>
          <s type="Number" name="bulletWidth">459</s>
          <s type="String" name="shootSoundUrl">pistol1/barrel4_sound</s>
          <s type="Boolean" name="lockB">false</s>
          <s type="Boolean" name="newB">false</s>
          <s type="String" name="color">purple</s>
          <s type="String" name="getTime">2025-07-29 14:31:49</s>
          <s type="Number" name="penetrationGap">0</s>
          <s type="Number" name="shakeAngle">0.5402839686721563</s>
          <s type="String" name="severTime">2025-07-29 14:29:42</s>
          <s type="String" name="inHouseTime"/>
          <s type="String" name="ele"/>
          <s type="Number" name="attackGap">0.3021990844979882</s>
          <s type="Array" name="skillArr">
            <s type="String" name="null">Kill_Crazy_ArmsSkill</s>
          </s>
          <s type="Number" name="bulletShakeWidth">19</s>
          <s type="Number" name="site">5</s>
          <s type="Array" name="godSkillArr"/>
          <s type="Object" name="partsSave">
            <s type="Array" name="arr"/>
            <s type="Object" name="lockObj"/>
            <s type="Number" name="lockLen">10</s>
            <s type="Number" name="gripMaxNum">15</s>
            <s type="Number" name="lastId">0</s>
          </s>
        </s>
        <s type="Object" name="null">
          <s type="Number" name="capacity">7</s>
          <s type="Object" name="critD">
            <s type="Number" name="mul">0</s>
            <s type="Number" name="pro">0</s>
          </s>
          <s type="Number" name="reloadGap">1.9993778432253748</s>
          <s type="Number" name="penetrationNum">1</s>
          <s type="String" name="cnName">冰雪野牛</s>
          <s type="Array" name="l"/>
          <s type="Boolean" name="firstChoiceB">false</s>
          <s type="Object" name="followD">
            <s type="Number" name="value">0</s>
            <s type="Number" name="maxTime">0</s>
            <s type="Boolean" name="hitIsTargetB">false</s>
            <s type="Boolean" name="noLM">false</s>
            <s type="Number" name="delay">0</s>
          </s>
          <s type="Number" name="strengthenLv">0</s>
          <s type="Number" name="addLevel">0</s>
          <s type="Number" name="shootAngle">17.793625289574265</s>
          <s type="String" name="id">0103_05_995_00000049</s>
          <s type="Object" name="o"/>
          <s type="Number" name="bulletNum">6</s>
          <s type="Number" name="hurtRatio">43</s>
          <s type="Number" name="eleLv">0</s>
          <s type="Number" name="itemsLevel">4</s>
          <s type="Number" name="bh">0</s>
          <s type="String" name="name">shotgun1</s>
          <s type="Boolean" name="upgradeB">true</s>
          <s type="Object" name="bounceD">
            <s type="Boolean" name="noDieB">false</s>
            <s type="String" name="shakeString"/>
            <s type="Number" name="vMul">1</s>
            <s type="Number" name="floor">0</s>
            <s type="Boolean" name="glueFloorB">false</s>
            <s type="Number" name="body">0</s>
            <s type="Number" name="hurtNumAdd">0</s>
            <s type="Number" name="noHitTime">0</s>
            <s type="Boolean" name="liveInitB">false</s>
          </s>
          <s type="Number" name="twoShootPro">0</s>
          <s type="String" name="itemsType">arms</s>
          <s type="String" name="s"/>
          <s type="Boolean" name="shopB">false</s>
          <s type="Number" name="sMaxLv">0</s>
          <s type="String" name="armsImgLabel">texture$m11_shotgun3$body_shotgun1$barrel3_shotgun1$grip_shotgun1$bullet_shotgun3$stock_0</s>
          <s type="Number" name="strengthenNum">0</s>
          <s type="Number" name="evoLv">1</s>
          <s type="Number" name="bulletWidth">559</s>
          <s type="String" name="shootSoundUrl">shotgun1/barrel3_sound</s>
          <s type="Boolean" name="lockB">false</s>
          <s type="Boolean" name="newB">false</s>
          <s type="String" name="color">purple</s>
          <s type="String" name="getTime">2025-07-29 14:31:49</s>
          <s type="Number" name="penetrationGap">0</s>
          <s type="Number" name="shakeAngle">0.8897845214232802</s>
          <s type="String" name="severTime">2025-07-29 14:29:42</s>
          <s type="String" name="inHouseTime"/>
          <s type="String" name="ele"/>
          <s type="Number" name="attackGap">0.8917849970981478</s>
          <s type="Array" name="skillArr">
            <s type="String" name="null">Hit_silence_ArmsSkill_shotgun</s>
          </s>
          <s type="Number" name="bulletShakeWidth">44</s>
          <s type="Number" name="site">7</s>
          <s type="Array" name="godSkillArr"/>
          <s type="Object" name="partsSave">
            <s type="Array" name="arr"/>
            <s type="Object" name="lockObj"/>
            <s type="Number" name="lockLen">10</s>
            <s type="Number" name="gripMaxNum">15</s>
            <s type="Number" name="lastId">0</s>
          </s>
        </s>
      </s>
      <s type="Object" name="lockObj"/>
      <s type="Number" name="lockLen">62</s>
      <s type="Number" name="gripMaxNum">112</s>
      <s type="Number" name="lastId">67</s>
    </s>
    <s type="Object" name="headCount">
      <s type="Object" name="continuousLoginDay">
        <s type="Number" name="value">0</s>
        <s type="String" name="timeStr"/>
        <s type="Boolean" name="todayAddB">false</s>
      </s>
      <s type="Number" name="armsRemakeNum">0</s>
      <s type="Number" name="headShotLevelNum">0</s>
      <s type="Array" name="topOneNameArr"/>
      <s type="Object" name="askAllRightNum">
        <s type="Number" name="value">0</s>
        <s type="String" name="timeStr"/>
        <s type="Boolean" name="todayAddB">false</s>
      </s>
      <s type="Number" name="petEvoNum">0</s>
      <s type="Number" name="equipRemakeNum">0</s>
      <s type="Number" name="vehicleEvoNum">0</s>
    </s>
    <s type="Object" name="love">
      <s type="Number" name="value">0</s>
      <s type="Array" name="likeHateArr"/>
      <s type="Number" name="allGivingNum">0</s>
      <s type="String" name="fashionAddTime"/>
      <s type="String" name="likeName"/>
      <s type="Boolean" name="getGirlGiftB8">false</s>
      <s type="Number" name="dieNum">0</s>
      <s type="Number" name="buyGivingNum">0</s>
      <s type="Number" name="todayGivingNum">0</s>
      <s type="Number" name="hideNum">0</s>
      <s type="String" name="hateName"/>
      <s type="Number" name="likeGiftNum">0</s>
      <s type="Array" name="talkArr"/>
      <s type="Number" name="hateGiftNum">0</s>
      <s type="Number" name="allGiftNum">0</s>
      <s type="String" name="beforeGetGiftTimeStr"/>
      <s type="Boolean" name="getGirlGiftB">false</s>
      <s type="Number" name="showNum">0</s>
    </s>
    <s type="Object" name="armsHouse">
      <s type="Array" name="arr"/>
      <s type="Object" name="lockObj"/>
      <s type="Number" name="lockLen">30</s>
      <s type="Number" name="gripMaxNum">324</s>
      <s type="Number" name="lastId">0</s>
    </s>
    <s type="Object" name="bossCard">
      <s type="Array" name="wePk"/>
      <s type="Number" name="rn">0</s>
      <s type="Number" name="on">0</s>
      <s type="Number" name="pkWin">0</s>
      <s type="Number" name="g6">0</s>
      <s type="Number" name="pkWinStar">0</s>
      <s type="Boolean" name="hideHB">true</s>
      <s type="Number" name="g7">0</s>
      <s type="Number" name="evo8">0</s>
      <s type="Number" name="starF">1</s>
      <s type="Number" name="evo6">0</s>
      <s type="Number" name="bag">0</s>
      <s type="Number" name="evo7">0</s>
      <s type="Number" name="pkStone">0</s>
      <s type="Number" name="t">0</s>
      <s type="Number" name="v">0</s>
      <s type="Array" name="arr"/>
      <s type="Number" name="hNum">0</s>
      <s type="Number" name="lastId">0</s>
      <s type="String" name="fid"/>
      <s type="Number" name="demV">0</s>
      <s type="Number" name="s7N">0</s>
      <s type="Array" name="giftIdA"/>
      <s type="Array" name="emPk"/>
      <s type="Number" name="sumN">0</s>
      <s type="Number" name="num">0</s>
      <s type="Boolean" name="autoDown">true</s>
    </s>
    <s type="Object" name="wilder">
      <s type="Number" name="keyNum">10</s>
      <s type="Object" name="obj"/>
      <s type="Boolean" name="todayClickB">false</s>
      <s type="Boolean" name="lootB">false</s>
    </s>
    <s type="Object" name="guide">
      <s type="Boolean" name="numlockKeyTip2">false</s>
      <s type="String" name="un">68883d2d8d438c3deadf24b8e5bf9b83</s>
      <s type="Boolean" name="blackMarketUnlock">false</s>
      <s type="Boolean" name="partsUnlock">false</s>
      <s type="Boolean" name="task">false</s>
      <s type="Boolean" name="firstSaveB">false</s>
      <s type="Boolean" name="arms">true</s>
      <s type="String" name="ui">786b95964cd4aa61a8a83131856afaaa</s>
      <s type="Boolean" name="first">false</s>
      <s type="Boolean" name="firstDoubleB">false</s>
      <s type="String" name="un2">1021423530</s>
      <s type="Boolean" name="petUnlock">false</s>
      <s type="Boolean" name="arenaInfo">false</s>
      <s type="Boolean" name="arenaSeasonInfo">false</s>
      <s type="Boolean" name="houseInfo">false</s>
      <s type="String" name="uu">8e90af8799ee014b7c2c938f359a0b58</s>
      <s type="Boolean" name="endlessInfo">false</s>
      <s type="Boolean" name="endless">false</s>
      <s type="Boolean" name="numlockKeyTip1">false</s>
      <s type="String" name="uu2">1021423530</s>
      <s type="Boolean" name="more">false</s>
      <s type="Boolean" name="equip">true</s>
      <s type="Boolean" name="mainTask">false</s>
      <s type="Boolean" name="kingTaskInfo">false</s>
      <s type="Boolean" name="skill">false</s>
      <s type="Boolean" name="extraTaskInfo">false</s>
      <s type="Boolean" name="arenaTestB">false</s>
    </s>
    <s type="Object" name="arms">
      <s type="Array" name="arr">
        <s type="Object" name="null">
          <s type="Number" name="capacity">7</s>
          <s type="Object" name="critD">
            <s type="Number" name="mul">0</s>
            <s type="Number" name="pro">0</s>
          </s>
          <s type="Number" name="reloadGap">2.44565417105332</s>
          <s type="Number" name="penetrationNum">2</s>
          <s type="String" name="cnName">雷电偷袭者</s>
          <s type="Array" name="l"/>
          <s type="Boolean" name="firstChoiceB">false</s>
          <s type="Object" name="followD">
            <s type="Number" name="value">0</s>
            <s type="Number" name="maxTime">10000</s>
            <s type="Boolean" name="hitIsTargetB">false</s>
            <s type="Boolean" name="noLM">false</s>
            <s type="Number" name="delay">0</s>
          </s>
          <s type="Number" name="strengthenLv">0</s>
          <s type="Number" name="addLevel">0</s>
          <s type="Number" name="shootAngle">0</s>
          <s type="String" name="id">0102_03_994_00000003</s>
          <s type="Object" name="o"/>
          <s type="Number" name="bulletNum">1</s>
          <s type="Number" name="hurtRatio">370</s>
          <s type="Number" name="eleLv">0</s>
          <s type="Number" name="itemsLevel">5</s>
          <s type="Number" name="bh">0</s>
          <s type="String" name="name">sniperRifle</s>
          <s type="Boolean" name="upgradeB">true</s>
          <s type="Object" name="bounceD">
            <s type="Boolean" name="noDieB">false</s>
            <s type="String" name="shakeString"/>
            <s type="Number" name="vMul">1</s>
            <s type="Number" name="floor">0</s>
            <s type="Boolean" name="glueFloorB">false</s>
            <s type="Number" name="body">2</s>
            <s type="Number" name="hurtNumAdd">0</s>
            <s type="Number" name="noHitTime">0</s>
            <s type="Boolean" name="liveInitB">false</s>
          </s>
          <s type="Number" name="twoShootPro">0.2372768602799624</s>
          <s type="String" name="itemsType">arms</s>
          <s type="String" name="s"/>
          <s type="Boolean" name="shopB">false</s>
          <s type="Number" name="sMaxLv">0</s>
          <s type="String" name="armsImgLabel">texture$t6_sniper1$body_sniper1$barrel2_sniper3$grip_sniper1$bullet_sniper2$stock_sniper3$glass</s>
          <s type="Number" name="strengthenNum">0</s>
          <s type="Number" name="evoLv">1</s>
          <s type="Number" name="bulletWidth">1025</s>
          <s type="String" name="shootSoundUrl">sniper1/barrel2_sound</s>
          <s type="Boolean" name="lockB">false</s>
          <s type="Boolean" name="newB">false</s>
          <s type="String" name="color">red</s>
          <s type="String" name="getTime">2025-07-29 14:31:48</s>
          <s type="Number" name="penetrationGap">0</s>
          <s type="Number" name="shakeAngle">0.5234232158400118</s>
          <s type="String" name="severTime">2025-07-29 14:29:42</s>
          <s type="String" name="inHouseTime"/>
          <s type="String" name="ele"/>
          <s type="Number" name="attackGap">1.4116589423269033</s>
          <s type="Array" name="skillArr">
            <s type="String" name="null">Hit_Spurting_ArmsSkill</s>
          </s>
          <s type="Number" name="bulletShakeWidth">7</s>
          <s type="Number" name="site">0</s>
          <s type="Array" name="godSkillArr">
            <s type="String" name="null">Hit_seckill_godArmsSkill_sniper</s>
          </s>
          <s type="Object" name="partsSave">
            <s type="Array" name="arr"/>
            <s type="Object" name="lockObj"/>
            <s type="Number" name="lockLen">10</s>
            <s type="Number" name="gripMaxNum">15</s>
            <s type="Number" name="lastId">0</s>
          </s>
        </s>
        <s type="Object" name="null">
          <s type="Number" name="capacity">7</s>
          <s type="Object" name="critD">
            <s type="Number" name="mul">2</s>
            <s type="Number" name="pro">0.1624517366755754</s>
          </s>
          <s type="Number" name="reloadGap">2.146687374683097</s>
          <s type="Number" name="penetrationNum">0</s>
          <s type="String" name="cnName">卑微之火</s>
          <s type="Array" name="l"/>
          <s type="Boolean" name="firstChoiceB">false</s>
          <s type="Object" name="followD">
            <s type="Number" name="value">0</s>
            <s type="Number" name="maxTime">10000</s>
            <s type="Boolean" name="hitIsTargetB">false</s>
            <s type="Boolean" name="noLM">false</s>
            <s type="Number" name="delay">0</s>
          </s>
          <s type="Number" name="strengthenLv">0</s>
          <s type="Number" name="addLevel">0</s>
          <s type="Number" name="shootAngle">0</s>
          <s type="String" name="id">0105_04_994_00000031</s>
          <s type="Object" name="o"/>
          <s type="Number" name="bulletNum">1</s>
          <s type="Number" name="hurtRatio">124</s>
          <s type="Number" name="eleLv">0</s>
          <s type="Number" name="itemsLevel">5</s>
          <s type="Number" name="bh">0</s>
          <s type="String" name="name">rocket1</s>
          <s type="Boolean" name="upgradeB">true</s>
          <s type="Object" name="bounceD">
            <s type="Boolean" name="noDieB">false</s>
            <s type="String" name="shakeString"/>
            <s type="Number" name="vMul">1</s>
            <s type="Number" name="floor">0</s>
            <s type="Boolean" name="glueFloorB">false</s>
            <s type="Number" name="body">0</s>
            <s type="Number" name="hurtNumAdd">0</s>
            <s type="Number" name="noHitTime">0</s>
            <s type="Boolean" name="liveInitB">false</s>
          </s>
          <s type="Number" name="twoShootPro">0.2258695531170815</s>
          <s type="String" name="itemsType">arms</s>
          <s type="String" name="s"/>
          <s type="Boolean" name="shopB">false</s>
          <s type="Number" name="sMaxLv">0</s>
          <s type="String" name="armsImgLabel">texture$m4_rocket$body2_rocket$barrel2_sniper2$grip_rocket$bullet_rocket$stock2_0</s>
          <s type="Number" name="strengthenNum">0</s>
          <s type="Number" name="evoLv">1</s>
          <s type="Number" name="bulletWidth">25</s>
          <s type="String" name="shootSoundUrl">rocket/barrel2_sound</s>
          <s type="Boolean" name="lockB">false</s>
          <s type="Boolean" name="newB">false</s>
          <s type="String" name="color">orange</s>
          <s type="String" name="getTime">2025-07-29 14:31:49</s>
          <s type="Number" name="penetrationGap">0</s>
          <s type="Number" name="shakeAngle">0.13869943702593446</s>
          <s type="String" name="severTime">2025-07-29 14:29:42</s>
          <s type="String" name="inHouseTime"/>
          <s type="String" name="ele"/>
          <s type="Number" name="attackGap">1.1620092095807195</s>
          <s type="Array" name="skillArr">
            <s type="String" name="null">Kill_Crazy_ArmsSkill</s>
          </s>
          <s type="Number" name="bulletShakeWidth">0</s>
          <s type="Number" name="site">1</s>
          <s type="Array" name="godSkillArr"/>
          <s type="Object" name="partsSave">
            <s type="Array" name="arr"/>
            <s type="Object" name="lockObj"/>
            <s type="Number" name="lockLen">10</s>
            <s type="Number" name="gripMaxNum">15</s>
            <s type="Number" name="lastId">0</s>
          </s>
        </s>
      </s>
      <s type="Object" name="lockObj"/>
      <s type="Number" name="lockLen">2</s>
      <s type="Number" name="gripMaxNum">6</s>
      <s type="Number" name="lastId">1</s>
    </s>
    <s type="Object" name="armsTor">
      <s type="Number" name="lastId">0</s>
      <s type="Boolean" name="hideHB">true</s>
      <s type="Array" name="arr"/>
      <s type="String" name="mId"/>
    </s>
    <s type="Object" name="equipHouse">
      <s type="Boolean" name="showFashionB">true</s>
      <s type="Object" name="lockObj"/>
      <s type="Number" name="lockLen">30</s>
      <s type="Array" name="arr"/>
      <s type="Number" name="gripMaxNum">384</s>
      <s type="Number" name="lastId">0</s>
    </s>
    <s type="Object" name="moreBag">
      <s type="Array" name="arr"/>
      <s type="Object" name="lockObj"/>
      <s type="Number" name="lockLen">10</s>
      <s type="Number" name="gripMaxNum">20</s>
      <s type="Number" name="lastId">0</s>
    </s>
    <s type="Object" name="partner">
      <s type="Object" name="ability">
        <s type="Object" name="saveObj"/>
      </s>
      <s type="Number" name="aiIndex">0</s>
      <s type="Number" name="exploit">0</s>
      <s type="Array" name="aiArr">
        <s type="Object" name="null">
          <s type="Object" name="obj"/>
        </s>
        <s type="Object" name="null">
          <s type="Object" name="obj"/>
        </s>
        <s type="Object" name="null">
          <s type="Object" name="obj"/>
        </s>
      </s>
      <s type="Number" name="dayExploit">0</s>
    </s>
    <s type="Object" name="drop">
      <s type="Number" name="refiningBlackNum">0</s>
      <s type="Number" name="allNintyArmsBlackNum">0</s>
      <s type="Number" name="timeCMore">0</s>
      <s type="Number" name="wuyiChip2021">0</s>
      <s type="Number" name="redBag">0</s>
      <s type="Number" name="chrisGun">0</s>
      <s type="Number" name="pumpkin20">0</s>
      <s type="Number" name="arbor21">0</s>
      <s type="Number" name="nintyArmsBlackNum">0</s>
      <s type="Number" name="dongzhi23">0</s>
      <s type="Number" name="nintyEquipBlackNum">0</s>
      <s type="Number" name="bloodStoneNum">0</s>
      <s type="Number" name="madheart">0</s>
      <s type="Number" name="mhA">0</s>
      <s type="Number" name="partsNum">0</s>
      <s type="Number" name="allBlackChipNum">23</s>
      <s type="Number" name="pumpkin20All">0</s>
      <s type="Number" name="dragonChestNum">0</s>
      <s type="Number" name="normalChestNum">0</s>
      <s type="Number" name="redBagAll">0</s>
      <s type="Number" name="magicChestNum">0</s>
      <s type="Object" name="dayObj">
        <s type="Object" name="saveObj"/>
      </s>
      <s type="Number" name="arbor21All">0</s>
      <s type="Number" name="armsBlackChipNum">0</s>
      <s type="Number" name="allArmsBlackChipNum">0</s>
      <s type="Number" name="blackChipNum">0</s>
      <s type="Number" name="ironChiefBook">0</s>
      <s type="Object" name="dayAll">
        <s type="Object" name="saveObj"/>
      </s>
      <s type="Number" name="allNintyEquipBlackNum">0</s>
      <s type="Number" name="tigerChest">0</s>
      <s type="Number" name="deviceNum">0</s>
      <s type="Object" name="weekObj">
        <s type="Object" name="saveObj"/>
      </s>
      <s type="Number" name="echelon1">0</s>
      <s type="Number" name="keyNum">0</s>
      <s type="Number" name="equipGem">0</s>
      <s type="Number" name="weaponNum">0</s>
    </s>
    <s type="Object" name="thingsBag">
      <s type="Array" name="arr">
        <s type="Object" name="null">
          <s type="Number" name="site">34</s>
          <s type="Boolean" name="autoUseB">false</s>
          <s type="String" name="itemsType">blackChip</s>
          <s type="String" name="name">yearPig</s>
          <s type="String" name="color">white</s>
          <s type="Number" name="itemsLevel">90</s>
          <s type="String" name="cnName">亥猪碎片</s>
          <s type="String" name="id">0301_214_00000000</s>
          <s type="Number" name="nowNum">5</s>
          <s type="Boolean" name="newB">false</s>
        </s>
        <s type="Object" name="null">
          <s type="Number" name="site">35</s>
          <s type="Boolean" name="autoUseB">false</s>
          <s type="String" name="itemsType">blackChip</s>
          <s type="String" name="name">yearDog</s>
          <s type="String" name="color">white</s>
          <s type="Number" name="itemsLevel">95</s>
          <s type="String" name="cnName">戌狗碎片</s>
          <s type="String" name="id">0301_215_00000001</s>
          <s type="Number" name="nowNum">8</s>
          <s type="Boolean" name="newB">false</s>
        </s>
        <s type="Object" name="null">
          <s type="Number" name="site">36</s>
          <s type="Boolean" name="autoUseB">false</s>
          <s type="String" name="itemsType">blackChip</s>
          <s type="String" name="name">yearSnake</s>
          <s type="String" name="color">white</s>
          <s type="Number" name="itemsLevel">97</s>
          <s type="String" name="cnName">巳蛇碎片</s>
          <s type="String" name="id">0301_216_00000002</s>
          <s type="Number" name="nowNum">5</s>
          <s type="Boolean" name="newB">false</s>
        </s>
        <s type="Object" name="null">
          <s type="Number" name="site">37</s>
          <s type="Boolean" name="autoUseB">false</s>
          <s type="String" name="itemsType">blackChip</s>
          <s type="String" name="name">yearHourse</s>
          <s type="String" name="color">white</s>
          <s type="Number" name="itemsLevel">97</s>
          <s type="String" name="cnName">午马碎片</s>
          <s type="String" name="id">0301_218_00000003</s>
          <s type="Number" name="nowNum">5</s>
          <s type="Boolean" name="newB">false</s>
        </s>
        <s type="Object" name="null">
          <s type="Number" name="site">31</s>
          <s type="Boolean" name="autoUseB">false</s>
          <s type="String" name="itemsType">materials</s>
          <s type="String" name="name">frozenGem</s>
          <s type="String" name="color">white</s>
          <s type="Number" name="itemsLevel">1</s>
          <s type="String" name="cnName">冷冻宝石</s>
          <s type="String" name="id">0301_159_00000004</s>
          <s type="Number" name="nowNum">8</s>
          <s type="Boolean" name="newB">false</s>
        </s>
        <s type="Object" name="null">
          <s type="Number" name="site">30</s>
          <s type="Boolean" name="autoUseB">false</s>
          <s type="String" name="itemsType">materials</s>
          <s type="String" name="name">electricGem</s>
          <s type="String" name="color">white</s>
          <s type="Number" name="itemsLevel">1</s>
          <s type="String" name="cnName">电磁宝石</s>
          <s type="String" name="id">0301_158_00000005</s>
          <s type="Number" name="nowNum">8</s>
          <s type="Boolean" name="newB">false</s>
        </s>
        <s type="Object" name="null">
          <s type="Number" name="site">29</s>
          <s type="Boolean" name="autoUseB">false</s>
          <s type="String" name="itemsType">materials</s>
          <s type="String" name="name">fireGem</s>
          <s type="String" name="color">white</s>
          <s type="Number" name="itemsLevel">1</s>
          <s type="String" name="cnName">火焰宝石</s>
          <s type="String" name="id">0301_157_00000006</s>
          <s type="Number" name="nowNum">8</s>
          <s type="Boolean" name="newB">false</s>
        </s>
        <s type="Object" name="null">
          <s type="Number" name="site">32</s>
          <s type="Boolean" name="autoUseB">false</s>
          <s type="String" name="itemsType">materials</s>
          <s type="String" name="name">poisonGem</s>
          <s type="String" name="color">white</s>
          <s type="Number" name="itemsLevel">1</s>
          <s type="String" name="cnName">生化宝石</s>
          <s type="String" name="id">0301_160_00000007</s>
          <s type="Number" name="nowNum">8</s>
          <s type="Boolean" name="newB">false</s>
        </s>
        <s type="Object" name="null">
          <s type="Number" name="site">18</s>
          <s type="Boolean" name="autoUseB">false</s>
          <s type="String" name="itemsType">materials</s>
          <s type="String" name="name">bloodStone</s>
          <s type="String" name="color">white</s>
          <s type="Number" name="itemsLevel">1</s>
          <s type="String" name="cnName">血石</s>
          <s type="String" name="id">0301_095_00000008</s>
          <s type="Number" name="nowNum">545</s>
          <s type="Boolean" name="newB">false</s>
        </s>
        <s type="Object" name="null">
          <s type="Number" name="site">17</s>
          <s type="Boolean" name="autoUseB">false</s>
          <s type="String" name="itemsType">materials</s>
          <s type="String" name="name">strengthenStone</s>
          <s type="String" name="color">white</s>
          <s type="Number" name="itemsLevel">1</s>
          <s type="String" name="cnName">强化石</s>
          <s type="String" name="id">0301_094_00000009</s>
          <s type="Number" name="nowNum">570</s>
          <s type="Boolean" name="newB">false</s>
        </s>
        <s type="Object" name="null">
          <s type="Number" name="site">15</s>
          <s type="Boolean" name="autoUseB">false</s>
          <s type="String" name="itemsType">materials</s>
          <s type="String" name="name">godStone</s>
          <s type="String" name="color">white</s>
          <s type="Number" name="itemsLevel">1</s>
          <s type="String" name="cnName">神能石</s>
          <s type="String" name="id">0301_090_00000010</s>
          <s type="Number" name="nowNum">1028</s>
          <s type="Boolean" name="newB">false</s>
        </s>
        <s type="Object" name="null">
          <s type="Number" name="site">16</s>
          <s type="Boolean" name="autoUseB">false</s>
          <s type="String" name="itemsType">materials</s>
          <s type="String" name="name">converStone</s>
          <s type="String" name="color">white</s>
          <s type="Number" name="itemsLevel">1</s>
          <s type="String" name="cnName">转化石</s>
          <s type="String" name="id">0301_093_00000011</s>
          <s type="Number" name="nowNum">93</s>
          <s type="Boolean" name="newB">false</s>
        </s>
        <s type="Object" name="null">
          <s type="Number" name="site">0</s>
          <s type="Boolean" name="autoUseB">false</s>
          <s type="String" name="itemsType">props</s>
          <s type="String" name="name">lifeBottle</s>
          <s type="String" name="color">white</s>
          <s type="Number" name="itemsLevel">1</s>
          <s type="String" name="cnName">生命药瓶</s>
          <s type="String" name="id">0301_000_00000012</s>
          <s type="Number" name="nowNum">6</s>
          <s type="Boolean" name="newB">false</s>
        </s>
        <s type="Object" name="null">
          <s type="Number" name="site">1</s>
          <s type="Boolean" name="autoUseB">false</s>
          <s type="String" name="itemsType">props</s>
          <s type="String" name="name">caisson</s>
          <s type="String" name="color">white</s>
          <s type="Number" name="itemsLevel">1</s>
          <s type="String" name="cnName">弹药箱</s>
          <s type="String" name="id">0301_002_00000013</s>
          <s type="Number" name="nowNum">6</s>
          <s type="Boolean" name="newB">false</s>
        </s>
        <s type="Object" name="null">
          <s type="Number" name="site">14</s>
          <s type="Boolean" name="autoUseB">false</s>
          <s type="String" name="itemsType">materials</s>
          <s type="String" name="name">skillStone</s>
          <s type="String" name="color">white</s>
          <s type="Number" name="itemsLevel">1</s>
          <s type="String" name="cnName">超能石</s>
          <s type="String" name="id">0301_089_00000016</s>
          <s type="Number" name="nowNum">2125</s>
          <s type="Boolean" name="newB">false</s>
        </s>
        <s type="Object" name="null">
          <s type="Number" name="site">5</s>
          <s type="Boolean" name="autoUseB">false</s>
          <s type="String" name="itemsType">props</s>
          <s type="String" name="name">armsBox</s>
          <s type="String" name="color">white</s>
          <s type="Number" name="itemsLevel">1</s>
          <s type="String" name="cnName">普通武器箱</s>
          <s type="String" name="id">0301_027_00000017</s>
          <s type="Number" name="nowNum">39</s>
          <s type="Boolean" name="newB">false</s>
        </s>
        <s type="Object" name="null">
          <s type="Number" name="site">7</s>
          <s type="Boolean" name="autoUseB">false</s>
          <s type="String" name="itemsType">props</s>
          <s type="String" name="name">equipBox</s>
          <s type="String" name="color">white</s>
          <s type="Number" name="itemsLevel">1</s>
          <s type="String" name="cnName">普通装备箱</s>
          <s type="String" name="id">0301_033_00000018</s>
          <s type="Number" name="nowNum">49</s>
          <s type="Boolean" name="newB">false</s>
        </s>
        <s type="Object" name="null">
          <s type="Number" name="site">23</s>
          <s type="Boolean" name="autoUseB">false</s>
          <s type="String" name="itemsType">materials</s>
          <s type="String" name="name">taxStamp</s>
          <s type="String" name="color">white</s>
          <s type="Number" name="itemsLevel">1</s>
          <s type="String" name="cnName">商券</s>
          <s type="String" name="id">0301_107_00000019</s>
          <s type="Number" name="nowNum">586</s>
          <s type="Boolean" name="newB">false</s>
        </s>
        <s type="Object" name="null">
          <s type="Number" name="site">24</s>
          <s type="Boolean" name="autoUseB">false</s>
          <s type="String" name="itemsType">materials</s>
          <s type="String" name="name">anniversaryCash</s>
          <s type="String" name="color">white</s>
          <s type="Number" name="itemsLevel">1</s>
          <s type="String" name="cnName">周年碎片</s>
          <s type="String" name="id">0301_122_00000020</s>
          <s type="Number" name="nowNum">67</s>
          <s type="Boolean" name="newB">false</s>
        </s>
        <s type="Object" name="null">
          <s type="Number" name="site">10</s>
          <s type="Boolean" name="autoUseB">false</s>
          <s type="String" name="itemsType">props</s>
          <s type="String" name="name">partsChest69</s>
          <s type="String" name="color">white</s>
          <s type="Number" name="itemsLevel">1</s>
          <s type="String" name="cnName">69级零件箱</s>
          <s type="String" name="id">0301_043_00000021</s>
          <s type="Number" name="nowNum">67</s>
          <s type="Boolean" name="newB">false</s>
        </s>
        <s type="Object" name="null">
          <s type="Number" name="site">20</s>
          <s type="Boolean" name="autoUseB">false</s>
          <s type="String" name="itemsType">materials</s>
          <s type="String" name="name">armsRadium</s>
          <s type="String" name="color">white</s>
          <s type="Number" name="itemsLevel">1</s>
          <s type="String" name="cnName">镭晶</s>
          <s type="String" name="id">0301_098_00000022</s>
          <s type="Number" name="nowNum">140</s>
          <s type="Boolean" name="newB">false</s>
        </s>
        <s type="Object" name="null">
          <s type="Number" name="site">26</s>
          <s type="Boolean" name="autoUseB">false</s>
          <s type="String" name="itemsType">materials</s>
          <s type="String" name="name">allBlackEquipCash</s>
          <s type="String" name="color">white</s>
          <s type="Number" name="itemsLevel">1</s>
          <s type="String" name="cnName">神护碎片</s>
          <s type="String" name="id">0301_124_00000023</s>
          <s type="Number" name="nowNum">240</s>
          <s type="Boolean" name="newB">false</s>
        </s>
        <s type="Object" name="null">
          <s type="Number" name="site">6</s>
          <s type="Boolean" name="autoUseB">false</s>
          <s type="String" name="itemsType">props</s>
          <s type="String" name="name">armsHighBox</s>
          <s type="String" name="color">white</s>
          <s type="Number" name="itemsLevel">1</s>
          <s type="String" name="cnName">高级武器箱</s>
          <s type="String" name="id">0301_028_00000024</s>
          <s type="Number" name="nowNum">78</s>
          <s type="Boolean" name="newB">false</s>
        </s>
        <s type="Object" name="null">
          <s type="Number" name="site">8</s>
          <s type="Boolean" name="autoUseB">false</s>
          <s type="String" name="itemsType">props</s>
          <s type="String" name="name">equipHighBox</s>
          <s type="String" name="color">white</s>
          <s type="Number" name="itemsLevel">1</s>
          <s type="String" name="cnName">高级装备箱</s>
          <s type="String" name="id">0301_034_00000025</s>
          <s type="Number" name="nowNum">50</s>
          <s type="Boolean" name="newB">false</s>
        </s>
        <s type="Object" name="null">
          <s type="Number" name="site">25</s>
          <s type="Boolean" name="autoUseB">false</s>
          <s type="String" name="itemsType">materials</s>
          <s type="String" name="name">allBlackCash</s>
          <s type="String" name="color">white</s>
          <s type="Number" name="itemsLevel">1</s>
          <s type="String" name="cnName">神武碎片</s>
          <s type="String" name="id">0301_123_00000026</s>
          <s type="Number" name="nowNum">240</s>
          <s type="Boolean" name="newB">false</s>
        </s>
        <s type="Object" name="null">
          <s type="Number" name="site">4</s>
          <s type="Boolean" name="autoUseB">false</s>
          <s type="String" name="itemsType">props</s>
          <s type="String" name="name">skillFleshCard</s>
          <s type="String" name="color">white</s>
          <s type="Number" name="itemsLevel">1</s>
          <s type="String" name="cnName">技能刷新卡</s>
          <s type="String" name="id">0301_006_00000027</s>
          <s type="Number" name="nowNum">13</s>
          <s type="Boolean" name="newB">false</s>
        </s>
        <s type="Object" name="null">
          <s type="Number" name="site">27</s>
          <s type="Boolean" name="autoUseB">false</s>
          <s type="String" name="itemsType">materials</s>
          <s type="String" name="name">dressStamp</s>
          <s type="String" name="color">white</s>
          <s type="Number" name="itemsLevel">1</s>
          <s type="String" name="cnName">装扮券</s>
          <s type="String" name="id">0301_132_00000028</s>
          <s type="Number" name="nowNum">1</s>
          <s type="Boolean" name="newB">false</s>
        </s>
        <s type="Object" name="null">
          <s type="Number" name="site">22</s>
          <s type="Boolean" name="autoUseB">false</s>
          <s type="String" name="itemsType">materials</s>
          <s type="String" name="name">arenaStamp</s>
          <s type="String" name="color">white</s>
          <s type="Number" name="itemsLevel">1</s>
          <s type="String" name="cnName">优胜券</s>
          <s type="String" name="id">0301_104_00000029</s>
          <s type="Number" name="nowNum">1850</s>
          <s type="Boolean" name="newB">false</s>
        </s>
        <s type="Object" name="null">
          <s type="Number" name="site">13</s>
          <s type="Boolean" name="autoUseB">false</s>
          <s type="String" name="itemsType">props</s>
          <s type="String" name="name">equipGemChest</s>
          <s type="String" name="color">white</s>
          <s type="Number" name="itemsLevel">1</s>
          <s type="String" name="cnName">装备宝石箱</s>
          <s type="String" name="id">0301_056_00000030</s>
          <s type="Number" name="nowNum">20</s>
          <s type="Boolean" name="newB">false</s>
        </s>
        <s type="Object" name="null">
          <s type="Number" name="site">28</s>
          <s type="Boolean" name="autoUseB">false</s>
          <s type="String" name="itemsType">materials</s>
          <s type="String" name="name">demBall</s>
          <s type="String" name="color">white</s>
          <s type="Number" name="itemsLevel">1</s>
          <s type="String" name="cnName">万能球</s>
          <s type="String" name="id">0301_150_00000031</s>
          <s type="Number" name="nowNum">240</s>
          <s type="Boolean" name="newB">false</s>
        </s>
        <s type="Object" name="null">
          <s type="Number" name="site">12</s>
          <s type="Boolean" name="autoUseB">false</s>
          <s type="String" name="itemsType">props</s>
          <s type="String" name="name">armsGemChest</s>
          <s type="String" name="color">white</s>
          <s type="Number" name="itemsLevel">1</s>
          <s type="String" name="cnName">武器宝石箱</s>
          <s type="String" name="id">0301_055_00000032</s>
          <s type="Number" name="nowNum">30</s>
          <s type="Boolean" name="newB">false</s>
        </s>
        <s type="Object" name="null">
          <s type="Number" name="site">33</s>
          <s type="Boolean" name="autoUseB">false</s>
          <s type="String" name="itemsType">props</s>
          <s type="String" name="name">exploitCard</s>
          <s type="String" name="color">white</s>
          <s type="Number" name="itemsLevel">1</s>
          <s type="String" name="cnName">队友功勋牌</s>
          <s type="String" name="id">0301_175_00000033</s>
          <s type="Number" name="nowNum">4</s>
          <s type="Boolean" name="newB">false</s>
        </s>
        <s type="Object" name="null">
          <s type="Number" name="site">21</s>
          <s type="Boolean" name="autoUseB">false</s>
          <s type="String" name="itemsType">materials</s>
          <s type="String" name="name">strengthenDrug</s>
          <s type="String" name="color">white</s>
          <s type="Number" name="itemsLevel">1</s>
          <s type="String" name="cnName">强化剂</s>
          <s type="String" name="id">0301_099_00000034</s>
          <s type="Number" name="nowNum">1000</s>
          <s type="Boolean" name="newB">false</s>
        </s>
        <s type="Object" name="null">
          <s type="Number" name="site">2</s>
          <s type="Boolean" name="autoUseB">false</s>
          <s type="String" name="itemsType">materials</s>
          <s type="String" name="name">rebirthStone</s>
          <s type="String" name="color">white</s>
          <s type="Number" name="itemsLevel">1</s>
          <s type="String" name="cnName">重生石</s>
          <s type="String" name="id">0301_004_00000035</s>
          <s type="Number" name="nowNum">12</s>
          <s type="Boolean" name="newB">false</s>
        </s>
        <s type="Object" name="null">
          <s type="Number" name="site">3</s>
          <s type="Boolean" name="autoUseB">false</s>
          <s type="String" name="itemsType">props</s>
          <s type="String" name="name">teamRebirthCard</s>
          <s type="String" name="color">white</s>
          <s type="Number" name="itemsLevel">1</s>
          <s type="String" name="cnName">队友重生卡</s>
          <s type="String" name="id">0301_005_00000036</s>
          <s type="Number" name="nowNum">12</s>
          <s type="Boolean" name="newB">false</s>
        </s>
        <s type="Object" name="null">
          <s type="Number" name="site">19</s>
          <s type="Boolean" name="autoUseB">false</s>
          <s type="String" name="itemsType">materials</s>
          <s type="String" name="name">armsTitanium</s>
          <s type="String" name="color">white</s>
          <s type="Number" name="itemsLevel">1</s>
          <s type="String" name="cnName">钛晶</s>
          <s type="String" name="id">0301_097_00000037</s>
          <s type="Number" name="nowNum">140</s>
          <s type="Boolean" name="newB">false</s>
        </s>
        <s type="Object" name="null">
          <s type="Number" name="site">9</s>
          <s type="Boolean" name="autoUseB">false</s>
          <s type="String" name="itemsType">props</s>
          <s type="String" name="name">coinHeap_1</s>
          <s type="String" name="color">white</s>
          <s type="Number" name="itemsLevel">1</s>
          <s type="String" name="cnName">小型银币堆</s>
          <s type="String" name="id">0301_035_00000038</s>
          <s type="Number" name="nowNum">3</s>
          <s type="Boolean" name="newB">false</s>
        </s>
      </s>
      <s type="Object" name="lockObj"/>
      <s type="Number" name="lockLen">600</s>
      <s type="Number" name="gripMaxNum">600</s>
      <s type="Number" name="lastId">40</s>
    </s>
    <s type="Object" name="food">
      <s type="Number" name="profiAll">0</s>
      <s type="Number" name="dropAll">1</s>
      <s type="Number" name="eatAll">0</s>
      <s type="Number" name="dropNum">1</s>
      <s type="Object" name="rawObj">
        <s type="Object" name="saveObj">
          <s type="Number" name="potato">1</s>
        </s>
      </s>
      <s type="Number" name="eatTime">0</s>
      <s type="Object" name="bookObj">
        <s type="Object" name="saveObj"/>
      </s>
      <s type="Array" name="eatNameArr"/>
      <s type="String" name="eatName"/>
      <s type="Number" name="profi">0</s>
      <s type="Number" name="eatNum">0</s>
    </s>
    <s type="Object" name="base">
      <s type="String" name="playerName">爆枪小战士</s>
      <s type="Boolean" name="lockB">false</s>
      <s type="Number" name="skillResetedNum">0</s>
      <s type="Number" name="level">10</s>
      <s type="Number" name="exp">14057</s>
      <s type="Boolean" name="playerCtrlB">true</s>
    </s>
    <s type="Object" name="arena">
      <s type="Boolean" name="tip284">false</s>
      <s type="Number" name="score">1</s>
      <s type="String" name="topName"/>
      <s type="Boolean" name="phaseOverTipB">false</s>
      <s type="Number" name="maxScore">0</s>
      <s type="Boolean" name="phaseGiftB">false</s>
      <s type="Number" name="todayNum">2</s>
      <s type="Number" name="streakNum">0</s>
      <s type="Boolean" name="topSetB">false</s>
      <s type="Object" name="uidObj"/>
      <s type="Number" name="newDayPhase">524</s>
      <s type="Object" name="unameObj"/>
      <s type="Number" name="canAddNum">0</s>
      <s type="Number" name="challengeNum">0</s>
      <s type="Number" name="before">0</s>
      <s type="Number" name="winNum">0</s>
      <s type="Number" name="allNum">0</s>
      <s type="Number" name="seasonFailNum">0</s>
      <s type="Number" name="nowPhase">0</s>
      <s type="Number" name="arenaStampNum">1850</s>
      <s type="Number" name="seasonWinNum">0</s>
      <s type="Object" name="record">
        <s type="Object" name="obj"/>
        <s type="Number" name="sortIndex">0</s>
      </s>
    </s>
    <s type="Object" name="partsBag">
      <s type="Array" name="arr">
        <s type="Object" name="null">
          <s type="Number" name="site">0</s>
          <s type="Boolean" name="autoUseB">false</s>
          <s type="String" name="itemsType">parts</s>
          <s type="String" name="name">loaderParts_3</s>
          <s type="String" name="color">white</s>
          <s type="Number" name="itemsLevel">3</s>
          <s type="String" name="cnName">小零件</s>
          <s type="String" name="id">0301_260_00000000</s>
          <s type="Number" name="nowNum">3</s>
          <s type="Boolean" name="newB">false</s>
        </s>
      </s>
      <s type="Object" name="lockObj"/>
      <s type="Number" name="lockLen">48</s>
      <s type="Number" name="gripMaxNum">144</s>
      <s type="Number" name="lastId">1</s>
    </s>
    <s type="Object" name="vip">
      <s type="Object" name="obj"/>
      <s type="Object" name="upLevelObj">
        <s type="Boolean" name="m50000">true</s>
        <s type="Boolean" name="m100">true</s>
        <s type="Boolean" name="m1000">true</s>
        <s type="Boolean" name="m5000">true</s>
        <s type="Boolean" name="m200">true</s>
        <s type="Boolean" name="m20000">true</s>
        <s type="Boolean" name="m8000">true</s>
        <s type="Boolean" name="m500">true</s>
        <s type="Boolean" name="m2000">true</s>
        <s type="Boolean" name="m10000">true</s>
      </s>
      <s type="Number" name="level">0</s>
      <s type="Object" name="dayObj"/>
    </s>
    <s type="Object" name="equipBag">
      <s type="Boolean" name="showFashionB">true</s>
      <s type="Object" name="lockObj"/>
      <s type="Number" name="lockLen">90</s>
      <s type="Array" name="arr">
        <s type="Object" name="null">
          <s type="String" name="itemsType">equip</s>
          <s type="String" name="imgName">greenFootballPlayer_belt</s>
          <s type="String" name="id">0214_05_996_00000010</s>
          <s type="Number" name="strengthenLv">0</s>
          <s type="Boolean" name="shopB">false</s>
          <s type="String" name="cnName">幻想橄榄护腰</s>
          <s type="Number" name="itemsLevel">3</s>
          <s type="Number" name="nowNum">1</s>
          <s type="Boolean" name="lockB">false</s>
          <s type="Number" name="sMaxLv">0</s>
          <s type="Number" name="strengthenNum">0</s>
          <s type="Number" name="evoLv">1</s>
          <s type="Boolean" name="newB">false</s>
          <s type="Number" name="proNum">0</s>
          <s type="Object" name="heroSkillAddObj"/>
          <s type="Object" name="obj">
            <s type="Number" name="capacity">1</s>
            <s type="Number" name="capacityMul">0.01</s>
            <s type="Number" name="reload">0.02</s>
            <s type="Number" name="charger">1</s>
          </s>
          <s type="String" name="severTime">2025-07-29 14:29:42</s>
          <s type="String" name="inHouseTime"/>
          <s type="String" name="name">greenFootballPlayer_belt</s>
          <s type="String" name="color">purple</s>
          <s type="String" name="getTime">2025-07-29 14:31:20</s>
          <s type="Array" name="skillArr"/>
          <s type="Number" name="site">11</s>
          <s type="Number" name="addLevel">0</s>
          <s type="String" name="partType">belt</s>
        </s>
        <s type="Object" name="null">
          <s type="String" name="itemsType">equip</s>
          <s type="String" name="imgName">blueWaist_coat</s>
          <s type="String" name="id">0212_05_995_00000016</s>
          <s type="Number" name="strengthenLv">0</s>
          <s type="Boolean" name="shopB">false</s>
          <s type="String" name="cnName">风行马甲</s>
          <s type="Number" name="itemsLevel">4</s>
          <s type="Number" name="nowNum">1</s>
          <s type="Boolean" name="lockB">false</s>
          <s type="Number" name="sMaxLv">0</s>
          <s type="Number" name="strengthenNum">0</s>
          <s type="Number" name="evoLv">1</s>
          <s type="Boolean" name="newB">false</s>
          <s type="Number" name="proNum">0</s>
          <s type="Object" name="heroSkillAddObj"/>
          <s type="Object" name="obj">
            <s type="Number" name="life">35</s>
            <s type="Number" name="hurtMul">0.02</s>
            <s type="Number" name="dpsMul">0.02</s>
            <s type="Number" name="dps">10</s>
          </s>
          <s type="String" name="severTime">2025-07-29 14:29:42</s>
          <s type="String" name="inHouseTime"/>
          <s type="String" name="name">blueWaist_coat</s>
          <s type="String" name="color">purple</s>
          <s type="String" name="getTime">2025-07-29 14:31:20</s>
          <s type="Array" name="skillArr"/>
          <s type="Number" name="site">6</s>
          <s type="Number" name="addLevel">0</s>
          <s type="String" name="partType">coat</s>
        </s>
        <s type="Object" name="null">
          <s type="String" name="itemsType">equip</s>
          <s type="String" name="imgName">greenFootballPlayer_pants</s>
          <s type="String" name="id">0213_05_996_00000018</s>
          <s type="Number" name="strengthenLv">0</s>
          <s type="Boolean" name="shopB">false</s>
          <s type="String" name="cnName">幻想橄榄护裤</s>
          <s type="Number" name="itemsLevel">3</s>
          <s type="Number" name="nowNum">1</s>
          <s type="Boolean" name="lockB">false</s>
          <s type="Number" name="sMaxLv">0</s>
          <s type="Number" name="strengthenNum">0</s>
          <s type="Number" name="evoLv">1</s>
          <s type="Boolean" name="newB">false</s>
          <s type="Number" name="proNum">0</s>
          <s type="Object" name="heroSkillAddObj"/>
          <s type="Object" name="obj">
            <s type="Number" name="life">19</s>
            <s type="Number" name="hurtMul">0.02</s>
            <s type="Number" name="dpsMul">0.02</s>
            <s type="Number" name="dps">4</s>
          </s>
          <s type="String" name="severTime">2025-07-29 14:29:42</s>
          <s type="String" name="inHouseTime"/>
          <s type="String" name="name">greenFootballPlayer_pants</s>
          <s type="String" name="color">purple</s>
          <s type="String" name="getTime">2025-07-29 14:31:20</s>
          <s type="Array" name="skillArr"/>
          <s type="Number" name="site">10</s>
          <s type="Number" name="addLevel">0</s>
          <s type="String" name="partType">pants</s>
        </s>
        <s type="Object" name="null">
          <s type="String" name="itemsType">equip</s>
          <s type="String" name="imgName">cityBoy_pants</s>
          <s type="String" name="id">0213_05_995_00000019</s>
          <s type="Number" name="strengthenLv">0</s>
          <s type="Boolean" name="shopB">false</s>
          <s type="String" name="cnName">沉默作训裤</s>
          <s type="Number" name="itemsLevel">4</s>
          <s type="Number" name="nowNum">1</s>
          <s type="Boolean" name="lockB">false</s>
          <s type="Number" name="sMaxLv">0</s>
          <s type="Number" name="strengthenNum">0</s>
          <s type="Number" name="evoLv">1</s>
          <s type="Boolean" name="newB">false</s>
          <s type="Number" name="proNum">0</s>
          <s type="Object" name="heroSkillAddObj"/>
          <s type="Object" name="obj">
            <s type="Number" name="lifeMul">0.02</s>
            <s type="Number" name="life">35</s>
            <s type="Number" name="hurtMul">0.02</s>
            <s type="Number" name="dps">10</s>
          </s>
          <s type="String" name="severTime">2025-07-29 14:29:42</s>
          <s type="String" name="inHouseTime"/>
          <s type="String" name="name">cityBoy_pants</s>
          <s type="String" name="color">purple</s>
          <s type="String" name="getTime">2025-07-29 14:31:20</s>
          <s type="Array" name="skillArr"/>
          <s type="Number" name="site">15</s>
          <s type="Number" name="addLevel">0</s>
          <s type="String" name="partType">pants</s>
        </s>
        <s type="Object" name="null">
          <s type="String" name="itemsType">equip</s>
          <s type="String" name="imgName">superMan_coat</s>
          <s type="String" name="id">0212_05_995_00000020</s>
          <s type="Number" name="strengthenLv">0</s>
          <s type="Boolean" name="shopB">false</s>
          <s type="String" name="cnName">蓝色T恤</s>
          <s type="Number" name="itemsLevel">4</s>
          <s type="Number" name="nowNum">1</s>
          <s type="Boolean" name="lockB">false</s>
          <s type="Number" name="sMaxLv">0</s>
          <s type="Number" name="strengthenNum">0</s>
          <s type="Number" name="evoLv">1</s>
          <s type="Boolean" name="newB">false</s>
          <s type="Number" name="proNum">0</s>
          <s type="Object" name="heroSkillAddObj"/>
          <s type="Object" name="obj">
            <s type="Number" name="life">26</s>
            <s type="Number" name="hurtMul">0.02</s>
            <s type="Number" name="dpsMul">0.02</s>
            <s type="Number" name="dps">10</s>
          </s>
          <s type="String" name="severTime">2025-07-29 14:29:42</s>
          <s type="String" name="inHouseTime"/>
          <s type="String" name="name">superMan_coat</s>
          <s type="String" name="color">purple</s>
          <s type="String" name="getTime">2025-07-29 14:31:20</s>
          <s type="Array" name="skillArr"/>
          <s type="Number" name="site">16</s>
          <s type="Number" name="addLevel">0</s>
          <s type="String" name="partType">coat</s>
        </s>
        <s type="Object" name="null">
          <s type="String" name="itemsType">equip</s>
          <s type="String" name="imgName">greenFootballPlayer_coat</s>
          <s type="String" name="id">0212_05_994_00000025</s>
          <s type="Number" name="strengthenLv">0</s>
          <s type="Boolean" name="shopB">false</s>
          <s type="String" name="cnName">幻想橄榄护衣</s>
          <s type="Number" name="itemsLevel">5</s>
          <s type="Number" name="nowNum">1</s>
          <s type="Boolean" name="lockB">false</s>
          <s type="Number" name="sMaxLv">0</s>
          <s type="Number" name="strengthenNum">0</s>
          <s type="Number" name="evoLv">1</s>
          <s type="Boolean" name="newB">false</s>
          <s type="Number" name="proNum">0</s>
          <s type="Object" name="heroSkillAddObj"/>
          <s type="Object" name="obj">
            <s type="Number" name="lifeMul">0.02</s>
            <s type="Number" name="hurt">5</s>
            <s type="Number" name="life">31</s>
            <s type="Number" name="dps">12</s>
          </s>
          <s type="String" name="severTime">2025-07-29 14:29:42</s>
          <s type="String" name="inHouseTime"/>
          <s type="String" name="name">greenFootballPlayer_coat</s>
          <s type="String" name="color">purple</s>
          <s type="String" name="getTime">2025-07-29 14:31:20</s>
          <s type="Array" name="skillArr"/>
          <s type="Number" name="site">8</s>
          <s type="Number" name="addLevel">0</s>
          <s type="String" name="partType">coat</s>
        </s>
        <s type="Object" name="null">
          <s type="String" name="itemsType">equip</s>
          <s type="String" name="imgName">greenFootballPlayer_coat</s>
          <s type="String" name="id">0212_05_996_00000028</s>
          <s type="Number" name="strengthenLv">0</s>
          <s type="Boolean" name="shopB">false</s>
          <s type="String" name="cnName">幻想橄榄护衣</s>
          <s type="Number" name="itemsLevel">3</s>
          <s type="Number" name="nowNum">1</s>
          <s type="Boolean" name="lockB">false</s>
          <s type="Number" name="sMaxLv">0</s>
          <s type="Number" name="strengthenNum">0</s>
          <s type="Number" name="evoLv">1</s>
          <s type="Boolean" name="newB">false</s>
          <s type="Number" name="proNum">0</s>
          <s type="Object" name="heroSkillAddObj"/>
          <s type="Object" name="obj">
            <s type="Number" name="lifeRate">1</s>
            <s type="Number" name="hurt">1</s>
            <s type="Number" name="life">20</s>
            <s type="Number" name="dps">3</s>
          </s>
          <s type="String" name="severTime">2025-07-29 14:29:42</s>
          <s type="String" name="inHouseTime"/>
          <s type="String" name="name">greenFootballPlayer_coat</s>
          <s type="String" name="color">purple</s>
          <s type="String" name="getTime">2025-07-29 14:31:20</s>
          <s type="Array" name="skillArr"/>
          <s type="Number" name="site">9</s>
          <s type="Number" name="addLevel">0</s>
          <s type="String" name="partType">coat</s>
        </s>
        <s type="Object" name="null">
          <s type="String" name="itemsType">equip</s>
          <s type="String" name="imgName">greenFootballPlayer_head</s>
          <s type="String" name="id">0211_05_994_00000029</s>
          <s type="Number" name="strengthenLv">0</s>
          <s type="Boolean" name="shopB">false</s>
          <s type="String" name="cnName">幻想橄榄盔</s>
          <s type="Number" name="itemsLevel">5</s>
          <s type="Number" name="nowNum">1</s>
          <s type="Boolean" name="lockB">false</s>
          <s type="Number" name="sMaxLv">0</s>
          <s type="Number" name="strengthenNum">0</s>
          <s type="Number" name="evoLv">1</s>
          <s type="Boolean" name="newB">false</s>
          <s type="Number" name="proNum">0</s>
          <s type="Object" name="heroSkillAddObj"/>
          <s type="Object" name="obj">
            <s type="Number" name="lifeMul">0.02</s>
            <s type="Number" name="life">45</s>
            <s type="Number" name="head">375</s>
            <s type="Number" name="headMul">0.3</s>
          </s>
          <s type="String" name="severTime">2025-07-29 14:29:42</s>
          <s type="String" name="inHouseTime"/>
          <s type="String" name="name">greenFootballPlayer_head</s>
          <s type="String" name="color">purple</s>
          <s type="String" name="getTime">2025-07-29 14:31:20</s>
          <s type="Array" name="skillArr"/>
          <s type="Number" name="site">7</s>
          <s type="Number" name="addLevel">0</s>
          <s type="String" name="partType">head</s>
        </s>
        <s type="Object" name="null">
          <s type="String" name="itemsType">equip</s>
          <s type="String" name="imgName">superMan_coat</s>
          <s type="String" name="id">0212_05_996_00000035</s>
          <s type="Number" name="strengthenLv">0</s>
          <s type="Boolean" name="shopB">false</s>
          <s type="String" name="cnName">蓝色T恤</s>
          <s type="Number" name="itemsLevel">3</s>
          <s type="Number" name="nowNum">1</s>
          <s type="Boolean" name="lockB">false</s>
          <s type="Number" name="sMaxLv">0</s>
          <s type="Number" name="strengthenNum">0</s>
          <s type="Number" name="evoLv">1</s>
          <s type="Boolean" name="newB">false</s>
          <s type="Number" name="proNum">0</s>
          <s type="Object" name="heroSkillAddObj"/>
          <s type="Object" name="obj">
            <s type="Number" name="lifeMul">0.02</s>
            <s type="Number" name="hurt">3</s>
            <s type="Number" name="life">12</s>
            <s type="Number" name="dps">9</s>
          </s>
          <s type="String" name="severTime">2025-07-29 14:29:42</s>
          <s type="String" name="inHouseTime"/>
          <s type="String" name="name">superMan_coat</s>
          <s type="String" name="color">purple</s>
          <s type="String" name="getTime">2025-07-29 14:31:20</s>
          <s type="Array" name="skillArr"/>
          <s type="Number" name="site">17</s>
          <s type="Number" name="addLevel">0</s>
          <s type="String" name="partType">coat</s>
        </s>
        <s type="Object" name="null">
          <s type="String" name="itemsType">equip</s>
          <s type="String" name="imgName">normalEquip_belt</s>
          <s type="String" name="id">0214_05_994_00000045</s>
          <s type="Number" name="strengthenLv">0</s>
          <s type="Boolean" name="shopB">false</s>
          <s type="String" name="cnName">褐皮带</s>
          <s type="Number" name="itemsLevel">5</s>
          <s type="Number" name="nowNum">1</s>
          <s type="Boolean" name="lockB">false</s>
          <s type="Number" name="sMaxLv">0</s>
          <s type="Number" name="strengthenNum">0</s>
          <s type="Number" name="evoLv">1</s>
          <s type="Boolean" name="newB">false</s>
          <s type="Number" name="proNum">0</s>
          <s type="Object" name="heroSkillAddObj"/>
          <s type="Object" name="obj">
            <s type="Number" name="capacity">1</s>
            <s type="Number" name="capacityMul">0.01</s>
            <s type="Number" name="chargerMul">0.01</s>
            <s type="Number" name="charger">1</s>
          </s>
          <s type="String" name="severTime">2025-07-29 14:29:42</s>
          <s type="String" name="inHouseTime"/>
          <s type="String" name="name">normalEquip_belt</s>
          <s type="String" name="color">purple</s>
          <s type="String" name="getTime">2025-07-29 14:31:20</s>
          <s type="Array" name="skillArr"/>
          <s type="Number" name="site">21</s>
          <s type="Number" name="addLevel">0</s>
          <s type="String" name="partType">belt</s>
        </s>
        <s type="Object" name="null">
          <s type="String" name="itemsType">equip</s>
          <s type="String" name="imgName">cityBoy_pants</s>
          <s type="String" name="id">0213_04_994_00000046</s>
          <s type="Number" name="strengthenLv">0</s>
          <s type="Boolean" name="shopB">false</s>
          <s type="String" name="cnName">沉默作训裤</s>
          <s type="Number" name="itemsLevel">5</s>
          <s type="Number" name="nowNum">1</s>
          <s type="Boolean" name="lockB">false</s>
          <s type="Number" name="sMaxLv">0</s>
          <s type="Number" name="strengthenNum">0</s>
          <s type="Number" name="evoLv">1</s>
          <s type="Boolean" name="newB">false</s>
          <s type="Number" name="proNum">0</s>
          <s type="Object" name="heroSkillAddObj"/>
          <s type="Object" name="obj">
            <s type="Number" name="lifeRate">2</s>
            <s type="Number" name="hurt">12</s>
            <s type="Number" name="life">38</s>
            <s type="Number" name="dpsMul">0.02</s>
            <s type="Number" name="dps">18</s>
          </s>
          <s type="String" name="severTime">2025-07-29 14:29:42</s>
          <s type="String" name="inHouseTime"/>
          <s type="String" name="name">cityBoy_pants</s>
          <s type="String" name="color">orange</s>
          <s type="String" name="getTime">2025-07-29 14:31:20</s>
          <s type="Array" name="skillArr"/>
          <s type="Number" name="site">14</s>
          <s type="Number" name="addLevel">0</s>
          <s type="String" name="partType">pants</s>
        </s>
        <s type="Object" name="null">
          <s type="String" name="itemsType">equip</s>
          <s type="String" name="imgName">cityBoy_coat</s>
          <s type="String" name="id">0212_05_994_00000063</s>
          <s type="Number" name="strengthenLv">0</s>
          <s type="Boolean" name="shopB">false</s>
          <s type="String" name="cnName">赤焰马甲</s>
          <s type="Number" name="itemsLevel">5</s>
          <s type="Number" name="nowNum">1</s>
          <s type="Boolean" name="lockB">false</s>
          <s type="Number" name="sMaxLv">0</s>
          <s type="Number" name="strengthenNum">0</s>
          <s type="Number" name="evoLv">1</s>
          <s type="Boolean" name="newB">false</s>
          <s type="Number" name="proNum">0</s>
          <s type="Object" name="heroSkillAddObj"/>
          <s type="Object" name="obj">
            <s type="Number" name="lifeRate">2</s>
            <s type="Number" name="life">21</s>
            <s type="Number" name="dpsMul">0.02</s>
            <s type="Number" name="dps">14</s>
          </s>
          <s type="String" name="severTime">2025-07-29 14:29:42</s>
          <s type="String" name="inHouseTime"/>
          <s type="String" name="name">cityBoy_coat</s>
          <s type="String" name="color">purple</s>
          <s type="String" name="getTime">2025-07-29 14:31:20</s>
          <s type="Array" name="skillArr"/>
          <s type="Number" name="site">13</s>
          <s type="Number" name="addLevel">0</s>
          <s type="String" name="partType">coat</s>
        </s>
        <s type="Object" name="null">
          <s type="String" name="itemsType">equip</s>
          <s type="String" name="imgName">superMan_coat</s>
          <s type="String" name="id">0212_05_996_00000067</s>
          <s type="Number" name="strengthenLv">0</s>
          <s type="Boolean" name="shopB">false</s>
          <s type="String" name="cnName">蓝色T恤</s>
          <s type="Number" name="itemsLevel">3</s>
          <s type="Number" name="nowNum">1</s>
          <s type="Boolean" name="lockB">false</s>
          <s type="Number" name="sMaxLv">0</s>
          <s type="Number" name="strengthenNum">0</s>
          <s type="Number" name="evoLv">1</s>
          <s type="Boolean" name="newB">false</s>
          <s type="Number" name="proNum">0</s>
          <s type="Object" name="heroSkillAddObj"/>
          <s type="Object" name="obj">
            <s type="Number" name="lifeMul">0.02</s>
            <s type="Number" name="life">10</s>
            <s type="Number" name="hurtMul">0.02</s>
            <s type="Number" name="dps">9</s>
          </s>
          <s type="String" name="severTime">2025-07-29 14:29:42</s>
          <s type="String" name="inHouseTime"/>
          <s type="String" name="name">superMan_coat</s>
          <s type="String" name="color">purple</s>
          <s type="String" name="getTime">2025-07-29 14:31:20</s>
          <s type="Array" name="skillArr"/>
          <s type="Number" name="site">18</s>
          <s type="Number" name="addLevel">0</s>
          <s type="String" name="partType">coat</s>
        </s>
        <s type="Object" name="null">
          <s type="String" name="itemsType">equip</s>
          <s type="String" name="imgName">normalEquip_belt</s>
          <s type="String" name="id">0214_04_994_00000071</s>
          <s type="Number" name="strengthenLv">0</s>
          <s type="Boolean" name="shopB">false</s>
          <s type="String" name="cnName">褐皮带</s>
          <s type="Number" name="itemsLevel">5</s>
          <s type="Number" name="nowNum">1</s>
          <s type="Boolean" name="lockB">false</s>
          <s type="Number" name="sMaxLv">0</s>
          <s type="Number" name="strengthenNum">0</s>
          <s type="Number" name="evoLv">1</s>
          <s type="Boolean" name="newB">false</s>
          <s type="Number" name="proNum">0</s>
          <s type="Object" name="heroSkillAddObj"/>
          <s type="Object" name="obj">
            <s type="Number" name="capacity">1</s>
            <s type="Number" name="capacityMul">0.01</s>
            <s type="Number" name="reload">0.04</s>
            <s type="Number" name="chargerMul">0.01</s>
            <s type="Number" name="charger">1</s>
          </s>
          <s type="String" name="severTime">2025-07-29 14:29:42</s>
          <s type="String" name="inHouseTime"/>
          <s type="String" name="name">normalEquip_belt</s>
          <s type="String" name="color">orange</s>
          <s type="String" name="getTime">2025-07-29 14:31:20</s>
          <s type="Array" name="skillArr"/>
          <s type="Number" name="site">20</s>
          <s type="Number" name="addLevel">0</s>
          <s type="String" name="partType">belt</s>
        </s>
        <s type="Object" name="null">
          <s type="String" name="itemsType">equip</s>
          <s type="String" name="imgName">footballPlayer_pants</s>
          <s type="String" name="id">0213_05_994_00000074</s>
          <s type="Number" name="strengthenLv">0</s>
          <s type="Boolean" name="shopB">false</s>
          <s type="String" name="cnName">勇气橄榄护裤</s>
          <s type="Number" name="itemsLevel">5</s>
          <s type="Number" name="nowNum">1</s>
          <s type="Boolean" name="lockB">false</s>
          <s type="Number" name="sMaxLv">0</s>
          <s type="Number" name="strengthenNum">0</s>
          <s type="Number" name="evoLv">1</s>
          <s type="Boolean" name="newB">false</s>
          <s type="Number" name="proNum">0</s>
          <s type="Object" name="heroSkillAddObj"/>
          <s type="Object" name="obj">
            <s type="Number" name="hurt">5</s>
            <s type="Number" name="life">37</s>
            <s type="Number" name="dpsMul">0.02</s>
            <s type="Number" name="dps">19</s>
          </s>
          <s type="String" name="severTime">2025-07-29 14:29:42</s>
          <s type="String" name="inHouseTime"/>
          <s type="String" name="name">footballPlayer_pants</s>
          <s type="String" name="color">purple</s>
          <s type="String" name="getTime">2025-07-29 14:31:20</s>
          <s type="Array" name="skillArr"/>
          <s type="Number" name="site">12</s>
          <s type="Number" name="addLevel">0</s>
          <s type="String" name="partType">pants</s>
        </s>
        <s type="Object" name="null">
          <s type="String" name="itemsType">equip</s>
          <s type="String" name="imgName">normalEquip_belt</s>
          <s type="String" name="id">0214_05_996_00000080</s>
          <s type="Number" name="strengthenLv">0</s>
          <s type="Boolean" name="shopB">false</s>
          <s type="String" name="cnName">褐皮带</s>
          <s type="Number" name="itemsLevel">3</s>
          <s type="Number" name="nowNum">1</s>
          <s type="Boolean" name="lockB">false</s>
          <s type="Number" name="sMaxLv">0</s>
          <s type="Number" name="strengthenNum">0</s>
          <s type="Number" name="evoLv">1</s>
          <s type="Boolean" name="newB">false</s>
          <s type="Number" name="proNum">0</s>
          <s type="Object" name="heroSkillAddObj"/>
          <s type="Object" name="obj">
            <s type="Number" name="capacity">1</s>
            <s type="Number" name="reload">0.01</s>
            <s type="Number" name="chargerMul">0.01</s>
            <s type="Number" name="charger">1</s>
          </s>
          <s type="String" name="severTime">2025-07-29 14:29:42</s>
          <s type="String" name="inHouseTime"/>
          <s type="String" name="name">normalEquip_belt</s>
          <s type="String" name="color">purple</s>
          <s type="String" name="getTime">2025-07-29 14:31:20</s>
          <s type="Array" name="skillArr"/>
          <s type="Number" name="site">22</s>
          <s type="Number" name="addLevel">0</s>
          <s type="String" name="partType">belt</s>
        </s>
        <s type="Object" name="null">
          <s type="String" name="itemsType">equip</s>
          <s type="String" name="imgName">normalEquip_belt</s>
          <s type="String" name="id">0214_04_995_00000082</s>
          <s type="Number" name="strengthenLv">0</s>
          <s type="Boolean" name="shopB">false</s>
          <s type="String" name="cnName">褐皮带</s>
          <s type="Number" name="itemsLevel">4</s>
          <s type="Number" name="nowNum">1</s>
          <s type="Boolean" name="lockB">false</s>
          <s type="Number" name="sMaxLv">0</s>
          <s type="Number" name="strengthenNum">0</s>
          <s type="Number" name="evoLv">1</s>
          <s type="Boolean" name="newB">false</s>
          <s type="Number" name="proNum">0</s>
          <s type="Object" name="heroSkillAddObj"/>
          <s type="Object" name="obj">
            <s type="Number" name="capacity">1</s>
            <s type="Number" name="capacityMul">0.01</s>
            <s type="Number" name="reload">0.02</s>
            <s type="Number" name="chargerMul">0.01</s>
            <s type="Number" name="charger">1</s>
          </s>
          <s type="String" name="severTime">2025-07-29 14:29:42</s>
          <s type="String" name="inHouseTime"/>
          <s type="String" name="name">normalEquip_belt</s>
          <s type="String" name="color">orange</s>
          <s type="String" name="getTime">2025-07-29 14:31:20</s>
          <s type="Array" name="skillArr"/>
          <s type="Number" name="site">19</s>
          <s type="Number" name="addLevel">0</s>
          <s type="String" name="partType">belt</s>
        </s>
        <s type="Object" name="null">
          <s type="String" name="itemsType">equip</s>
          <s type="String" name="imgName">hookDevice_1</s>
          <s type="String" name="id">0204_08_998_00000091</s>
          <s type="Number" name="strengthenLv">0</s>
          <s type="Boolean" name="shopB">false</s>
          <s type="String" name="cnName">斩之核心</s>
          <s type="Number" name="itemsLevel">1</s>
          <s type="Number" name="nowNum">1</s>
          <s type="Boolean" name="lockB">false</s>
          <s type="Number" name="sMaxLv">0</s>
          <s type="Number" name="strengthenNum">0</s>
          <s type="Number" name="evoLv">1</s>
          <s type="Boolean" name="newB">false</s>
          <s type="Number" name="proNum">0</s>
          <s type="Object" name="heroSkillAddObj"/>
          <s type="Object" name="obj">
            <s type="Number" name="dpsMul">0.105</s>
            <s type="Number" name="lifeMul">0.105</s>
          </s>
          <s type="String" name="severTime">2025-07-29 14:29:42</s>
          <s type="String" name="inHouseTime"/>
          <s type="String" name="name">hookDevice_1</s>
          <s type="String" name="color">white</s>
          <s type="String" name="getTime">2025-07-29 14:32:05</s>
          <s type="Array" name="skillArr">
            <s type="String" name="null">hookDevice_1</s>
          </s>
          <s type="Number" name="site">3</s>
          <s type="Number" name="addLevel">0</s>
          <s type="String" name="partType">device</s>
        </s>
        <s type="Object" name="null">
          <s type="String" name="itemsType">equip</s>
          <s type="String" name="imgName">longGlasses_1</s>
          <s type="String" name="id">0205_08_998_00000092</s>
          <s type="Number" name="strengthenLv">0</s>
          <s type="Boolean" name="shopB">false</s>
          <s type="String" name="cnName">远视镜</s>
          <s type="Number" name="itemsLevel">1</s>
          <s type="Number" name="nowNum">1</s>
          <s type="Boolean" name="lockB">false</s>
          <s type="Number" name="sMaxLv">0</s>
          <s type="Number" name="strengthenNum">0</s>
          <s type="Number" name="evoLv">1</s>
          <s type="Boolean" name="newB">false</s>
          <s type="Number" name="proNum">0</s>
          <s type="Object" name="heroSkillAddObj"/>
          <s type="Object" name="obj">
            <s type="Number" name="damageMul">0.12</s>
          </s>
          <s type="String" name="severTime">2025-07-29 14:29:42</s>
          <s type="String" name="inHouseTime"/>
          <s type="String" name="name">longGlasses_1</s>
          <s type="String" name="color">white</s>
          <s type="String" name="getTime">2025-07-29 14:32:08</s>
          <s type="Array" name="skillArr">
            <s type="String" name="null">longGlasses_jewelry_1</s>
          </s>
          <s type="Number" name="site">4</s>
          <s type="Number" name="addLevel">0</s>
          <s type="String" name="partType">jewelry</s>
        </s>
        <s type="Object" name="null">
          <s type="String" name="itemsType">equip</s>
          <s type="String" name="imgName">wolongStick_1</s>
          <s type="String" name="id">0203_08_998_00000093</s>
          <s type="Number" name="strengthenLv">0</s>
          <s type="Boolean" name="shopB">false</s>
          <s type="String" name="cnName">沃龙牌棍棒</s>
          <s type="Number" name="itemsLevel">1</s>
          <s type="Number" name="nowNum">1</s>
          <s type="String" name="s"/>
          <s type="Boolean" name="lockB">false</s>
          <s type="Number" name="sMaxLv">0</s>
          <s type="Boolean" name="cf">false</s>
          <s type="Number" name="strengthenNum">0</s>
          <s type="Number" name="evoLv">1</s>
          <s type="Boolean" name="newB">false</s>
          <s type="Number" name="proNum">0</s>
          <s type="String" name="getTime">2025-07-29 14:33:48</s>
          <s type="Object" name="obj">
            <s type="Number" name="dpsMul">0.01</s>
          </s>
          <s type="String" name="severTime">2025-07-29 14:29:42</s>
          <s type="String" name="inHouseTime"/>
          <s type="Number" name="addLevel">0</s>
          <s type="String" name="color">white</s>
          <s type="Object" name="heroSkillAddObj"/>
          <s type="Array" name="skillArr"/>
          <s type="Number" name="site">1</s>
          <s type="String" name="name">wolongStick_1</s>
          <s type="String" name="partType">weapon</s>
        </s>
        <s type="Object" name="null">
          <s type="String" name="itemsType">equip</s>
          <s type="String" name="imgName">moonDagger_1</s>
          <s type="String" name="id">0203_08_998_00000094</s>
          <s type="Number" name="strengthenLv">0</s>
          <s type="Boolean" name="shopB">false</s>
          <s type="String" name="cnName">月刺</s>
          <s type="Number" name="itemsLevel">1</s>
          <s type="Number" name="nowNum">2</s>
          <s type="String" name="s"/>
          <s type="Boolean" name="lockB">false</s>
          <s type="Number" name="sMaxLv">0</s>
          <s type="Boolean" name="cf">false</s>
          <s type="Number" name="strengthenNum">0</s>
          <s type="Number" name="evoLv">1</s>
          <s type="Boolean" name="newB">false</s>
          <s type="Number" name="proNum">0</s>
          <s type="String" name="getTime">2025-07-29 14:33:51</s>
          <s type="Object" name="obj">
            <s type="Number" name="dpsMul">0.01</s>
          </s>
          <s type="String" name="severTime">2025-07-29 14:29:42</s>
          <s type="String" name="inHouseTime"/>
          <s type="Number" name="addLevel">0</s>
          <s type="String" name="color">white</s>
          <s type="Object" name="heroSkillAddObj"/>
          <s type="Array" name="skillArr"/>
          <s type="Number" name="site">2</s>
          <s type="String" name="name">moonDagger_1</s>
          <s type="String" name="partType">weapon</s>
        </s>
        <s type="Object" name="null">
          <s type="String" name="itemsType">equip</s>
          <s type="String" name="imgName">butcherBlade_1</s>
          <s type="String" name="id">0203_08_998_00000095</s>
          <s type="Number" name="strengthenLv">0</s>
          <s type="Boolean" name="shopB">false</s>
          <s type="String" name="cnName">屠夫</s>
          <s type="Number" name="itemsLevel">1</s>
          <s type="Number" name="nowNum">2</s>
          <s type="String" name="s"/>
          <s type="Boolean" name="lockB">false</s>
          <s type="Number" name="sMaxLv">0</s>
          <s type="Boolean" name="cf">false</s>
          <s type="Number" name="strengthenNum">0</s>
          <s type="Number" name="evoLv">1</s>
          <s type="Boolean" name="newB">false</s>
          <s type="Number" name="proNum">0</s>
          <s type="String" name="getTime">2025-07-29 14:33:51</s>
          <s type="Object" name="obj">
            <s type="Number" name="dpsMul">0.01</s>
          </s>
          <s type="String" name="severTime">2025-07-29 14:29:42</s>
          <s type="String" name="inHouseTime"/>
          <s type="Number" name="addLevel">0</s>
          <s type="String" name="color">white</s>
          <s type="Object" name="heroSkillAddObj"/>
          <s type="Array" name="skillArr"/>
          <s type="Number" name="site">0</s>
          <s type="String" name="name">butcherBlade_1</s>
          <s type="String" name="partType">weapon</s>
        </s>
        <s type="Object" name="null">
          <s type="String" name="itemsType">equip</s>
          <s type="String" name="imgName">superGlasses</s>
          <s type="String" name="id">0205_08_998_00000000</s>
          <s type="Number" name="strengthenLv">0</s>
          <s type="Boolean" name="shopB">true</s>
          <s type="String" name="cnName">超远视镜</s>
          <s type="Number" name="itemsLevel">1</s>
          <s type="Number" name="nowNum">1</s>
          <s type="Boolean" name="lockB">true</s>
          <s type="Number" name="sMaxLv">0</s>
          <s type="Number" name="strengthenNum">0</s>
          <s type="Number" name="evoLv">1</s>
          <s type="Boolean" name="newB">false</s>
          <s type="Number" name="proNum">0</s>
          <s type="Object" name="heroSkillAddObj"/>
          <s type="Object" name="obj">
            <s type="Number" name="damageMul">0.18</s>
          </s>
          <s type="String" name="severTime">2025-07-29 14:26:29</s>
          <s type="String" name="inHouseTime"/>
          <s type="String" name="name">superGlasses</s>
          <s type="String" name="color">white</s>
          <s type="String" name="getTime">2025-07-29 14:26:26</s>
          <s type="Array" name="skillArr">
            <s type="String" name="null">superGlasses_jewelry</s>
          </s>
          <s type="Number" name="site">5</s>
          <s type="Number" name="addLevel">0</s>
          <s type="String" name="partType">jewelry</s>
        </s>
      </s>
      <s type="Number" name="gripMaxNum">180</s>
      <s type="Number" name="lastId">96</s>
    </s>
    <s type="Object" name="count">
      <s type="Number" name="skillGiftNum">0</s>
      <s type="Number" name="todayExploitCards">0</s>
      <s type="Number" name="hyperopiaMaxMul">0</s>
      <s type="Boolean" name="hurtB">false</s>
      <s type="Number" name="glidingTime">0</s>
      <s type="Number" name="blackMarketMaxNum">0</s>
      <s type="Number" name="killBossNum">1</s>
      <s type="Number" name="lotteryCoin">1</s>
      <s type="Number" name="onlineTime">373</s>
      <s type="Number" name="lotteryAllOrangeNum">0</s>
      <s type="Number" name="killNum">120</s>
      <s type="Number" name="bulletNum">0</s>
      <s type="Number" name="maxFallHigh">178</s>
      <s type="Number" name="onlineTime20">0</s>
      <s type="Array" name="task5NameArr"/>
      <s type="Array" name="killKingNameArr"/>
      <s type="Number" name="vehicleKillNum">0</s>
      <s type="Number" name="dropArmsNum">4</s>
      <s type="Number" name="todayStrengthNum">0</s>
      <s type="Number" name="weaponKillNum">0</s>
      <s type="Number" name="dropEquipNum">0</s>
      <s type="Number" name="todaySkillStoneNum">0</s>
      <s type="Number" name="bossNoOrangeNum">1</s>
      <s type="Number" name="levelOnlineTime">141</s>
      <s type="Number" name="maxFallValue">0</s>
      <s type="Number" name="todayKillEnemyNum">181</s>
      <s type="Number" name="doubleOnlineTime">0</s>
      <s type="Number" name="maxKingLevel">0</s>
      <s type="Number" name="maxFlyHigh">189</s>
      <s type="Number" name="charmMaxNum">0</s>
      <s type="Number" name="todayArenaNum">0</s>
      <s type="Number" name="rollNum">0</s>
      <s type="Number" name="normalLevelNum">2</s>
      <s type="Number" name="myopiaMaxMul">0</s>
      <s type="Number" name="todayUnionContribution">0</s>
      <s type="Number" name="todayArenaStamp">0</s>
      <s type="Number" name="todayUseMoney">1572</s>
      <s type="Number" name="moreMissileMaxNum">0</s>
      <s type="Boolean" name="oneHB">false</s>
      <s type="Number" name="dieNum">0</s>
    </s>
    <s type="Object" name="blackMarket">
      <s type="Object" name="arms">
        <s type="Array" name="arr"/>
        <s type="Object" name="lockObj"/>
        <s type="Number" name="lockLen">999</s>
        <s type="Number" name="gripMaxNum">999</s>
        <s type="Number" name="lastId">0</s>
      </s>
      <s type="Number" name="fleshNum">0</s>
      <s type="Number" name="buyNum">0</s>
      <s type="Object" name="equip">
        <s type="Boolean" name="showFashionB">true</s>
        <s type="Object" name="lockObj"/>
        <s type="Number" name="lockLen">999</s>
        <s type="Array" name="arr"/>
        <s type="Number" name="gripMaxNum">999</s>
        <s type="Number" name="lastId">0</s>
      </s>
      <s type="Boolean" name="addB">false</s>
      <s type="Array" name="goodsNameArr"/>
    </s>
    <s type="Object" name="space">
      <s type="Object" name="c">
        <s type="Object" name="saveObj"/>
      </s>
      <s type="Object" name="craft">
        <s type="Boolean" name="hideHB">true</s>
        <s type="Array" name="arr">
          <s type="Object" name="null">
            <s type="String" name="n">SilverShip</s>
            <s type="Object" name="p0">
              <s type="Object" name="saveObj">
                <s type="Number" name="silverScreen">1</s>
              </s>
            </s>
            <s type="Number" name="exp">0</s>
            <s type="String" name="now">0</s>
            <s type="Number" name="lv">1</s>
          </s>
        </s>
        <s type="String" name="now">SilverShip</s>
        <s type="Number" name="lastId">1</s>
      </s>
      <s type="Object" name="mapO"/>
      <s type="Array" name="cgArr"/>
    </s>
    <s type="Object" name="equip">
      <s type="Boolean" name="showFashionB">true</s>
      <s type="Object" name="lockObj"/>
      <s type="Number" name="lockLen">10</s>
      <s type="Array" name="arr">
        <s type="Object" name="null">
          <s type="String" name="itemsType">equip</s>
          <s type="String" name="imgName">footballPlayer_head</s>
          <s type="String" name="id">0211_04_994_00000034</s>
          <s type="Number" name="strengthenLv">0</s>
          <s type="Boolean" name="shopB">false</s>
          <s type="String" name="cnName">勇气橄榄盔</s>
          <s type="Number" name="itemsLevel">5</s>
          <s type="Number" name="nowNum">1</s>
          <s type="Boolean" name="lockB">false</s>
          <s type="Number" name="sMaxLv">0</s>
          <s type="Number" name="strengthenNum">0</s>
          <s type="Number" name="evoLv">1</s>
          <s type="Boolean" name="newB">false</s>
          <s type="Number" name="proNum">0</s>
          <s type="Object" name="heroSkillAddObj"/>
          <s type="Object" name="obj">
            <s type="Number" name="lifeRate">2</s>
            <s type="Number" name="lifeMul">0.02</s>
            <s type="Number" name="life">45</s>
            <s type="Number" name="head">314</s>
            <s type="Number" name="headMul">0.3</s>
          </s>
          <s type="String" name="severTime">2025-07-29 14:29:42</s>
          <s type="String" name="inHouseTime"/>
          <s type="String" name="name">footballPlayer_head</s>
          <s type="String" name="color">orange</s>
          <s type="String" name="getTime">2025-07-29 14:31:20</s>
          <s type="Array" name="skillArr"/>
          <s type="Number" name="site">0</s>
          <s type="Number" name="addLevel">0</s>
          <s type="String" name="partType">head</s>
        </s>
        <s type="Object" name="null">
          <s type="String" name="itemsType">equip</s>
          <s type="String" name="imgName">cityBoy_coat</s>
          <s type="String" name="id">0212_04_994_00000036</s>
          <s type="Number" name="strengthenLv">0</s>
          <s type="Boolean" name="shopB">false</s>
          <s type="String" name="cnName">赤焰马甲</s>
          <s type="Number" name="itemsLevel">5</s>
          <s type="Number" name="nowNum">1</s>
          <s type="Boolean" name="lockB">false</s>
          <s type="Number" name="sMaxLv">0</s>
          <s type="Number" name="strengthenNum">0</s>
          <s type="Number" name="evoLv">1</s>
          <s type="Boolean" name="newB">false</s>
          <s type="Number" name="proNum">0</s>
          <s type="Object" name="heroSkillAddObj"/>
          <s type="Object" name="obj">
            <s type="Number" name="hurt">12</s>
            <s type="Number" name="lifeMul">0.02</s>
            <s type="Number" name="hurtMul">0.02</s>
            <s type="Number" name="life">39</s>
            <s type="Number" name="dps">19</s>
          </s>
          <s type="String" name="severTime">2025-07-29 14:29:42</s>
          <s type="String" name="inHouseTime"/>
          <s type="String" name="name">cityBoy_coat</s>
          <s type="String" name="color">orange</s>
          <s type="String" name="getTime">2025-07-29 14:31:20</s>
          <s type="Array" name="skillArr"/>
          <s type="Number" name="site">1</s>
          <s type="Number" name="addLevel">0</s>
          <s type="String" name="partType">coat</s>
        </s>
        <s type="Object" name="null">
          <s type="String" name="itemsType">equip</s>
          <s type="String" name="imgName">footballPlayer_pants</s>
          <s type="String" name="id">0213_04_994_00000032</s>
          <s type="Number" name="strengthenLv">0</s>
          <s type="Boolean" name="shopB">false</s>
          <s type="String" name="cnName">勇气橄榄护裤</s>
          <s type="Number" name="itemsLevel">5</s>
          <s type="Number" name="nowNum">1</s>
          <s type="Boolean" name="lockB">false</s>
          <s type="Number" name="sMaxLv">0</s>
          <s type="Number" name="strengthenNum">0</s>
          <s type="Number" name="evoLv">1</s>
          <s type="Boolean" name="newB">false</s>
          <s type="Number" name="proNum">0</s>
          <s type="Object" name="heroSkillAddObj"/>
          <s type="Object" name="obj">
            <s type="Number" name="lifeRate">2</s>
            <s type="Number" name="hurt">12</s>
            <s type="Number" name="life">40</s>
            <s type="Number" name="lifeMul">0.02</s>
            <s type="Number" name="dps">20</s>
          </s>
          <s type="String" name="severTime">2025-07-29 14:29:42</s>
          <s type="String" name="inHouseTime"/>
          <s type="String" name="name">footballPlayer_pants</s>
          <s type="String" name="color">orange</s>
          <s type="String" name="getTime">2025-07-29 14:31:20</s>
          <s type="Array" name="skillArr"/>
          <s type="Number" name="site">2</s>
          <s type="Number" name="addLevel">0</s>
          <s type="String" name="partType">pants</s>
        </s>
        <s type="Object" name="null">
          <s type="String" name="itemsType">equip</s>
          <s type="String" name="imgName">greenFootballPlayer_belt</s>
          <s type="String" name="id">0214_04_994_00000031</s>
          <s type="Number" name="strengthenLv">0</s>
          <s type="Boolean" name="shopB">false</s>
          <s type="String" name="cnName">幻想橄榄护腰</s>
          <s type="Number" name="itemsLevel">5</s>
          <s type="Number" name="nowNum">1</s>
          <s type="Boolean" name="lockB">false</s>
          <s type="Number" name="sMaxLv">0</s>
          <s type="Number" name="strengthenNum">0</s>
          <s type="Number" name="evoLv">1</s>
          <s type="Boolean" name="newB">false</s>
          <s type="Number" name="proNum">0</s>
          <s type="Object" name="heroSkillAddObj"/>
          <s type="Object" name="obj">
            <s type="Number" name="capacity">1</s>
            <s type="Number" name="capacityMul">0.01</s>
            <s type="Number" name="reload">0.03</s>
            <s type="Number" name="chargerMul">0.01</s>
            <s type="Number" name="charger">1</s>
          </s>
          <s type="String" name="severTime">2025-07-29 14:29:42</s>
          <s type="String" name="inHouseTime"/>
          <s type="String" name="name">greenFootballPlayer_belt</s>
          <s type="String" name="color">orange</s>
          <s type="String" name="getTime">2025-07-29 14:31:20</s>
          <s type="Array" name="skillArr"/>
          <s type="Number" name="site">3</s>
          <s type="Number" name="addLevel">0</s>
          <s type="String" name="partType">belt</s>
        </s>
      </s>
      <s type="Number" name="gripMaxNum">10</s>
      <s type="Number" name="lastId">0</s>
    </s>
    <s type="Object" name="ask">
      <s type="Boolean" name="overB">false</s>
      <s type="Number" name="errorNum">0</s>
      <s type="Number" name="score">0</s>
      <s type="String" name="nowSeason">20256</s>
      <s type="Number" name="todayScore">0</s>
      <s type="Number" name="todayTime">0</s>
      <s type="Boolean" name="giftB">false</s>
      <s type="Number" name="correctNum">0</s>
      <s type="String" name="nowSaveVersion">1.0</s>
    </s>
    <s type="Object" name="pay">
      <s type="Object" name="obj">
        <s type="Number" name="4511">1</s>
        <s type="Number" name="4471">50</s>
        <s type="Number" name="4435">150</s>
        <s type="Number" name="1391">28</s>
      </s>
      <s type="Number" name="ver">1.7</s>
    </s>
    <s type="Object" name="post">
      <s type="Number" name="postLv">0</s>
      <s type="Number" name="superGiftAdd">0</s>
      <s type="Number" name="extraDayNum">0</s>
      <s type="Boolean" name="dropB">true</s>
      <s type="String" name="firstTime"/>
      <s type="Number" name="postExp">0</s>
      <s type="String" name="giftTime"/>
      <s type="String" name="tipStr"/>
      <s type="String" name="expTime"/>
      <s type="Number" name="totalGiftExp">0</s>
      <s type="Number" name="expDay">0</s>
      <s type="Number" name="bribe">0</s>
      <s type="String" name="nowPost"/>
      <s type="Array" name="cardRecordArr"/>
      <s type="Number" name="normalGiftBribe">0</s>
      <s type="Array" name="allCardRecordArr"/>
      <s type="Number" name="superGiftBribe">0</s>
      <s type="Number" name="normalGiftAdd">0</s>
    </s>
    <s type="Object" name="achieve">
      <s type="Object" name="obj"/>
      <s type="Boolean" name="onlyNoCompleteB">false</s>
    </s>
    <s type="Object" name="time">
      <s type="Number" name="week6">144</s>
      <s type="Number" name="doubleMaterialsDropTime">0</s>
      <s type="Number" name="doubleExpTime">0</s>
      <s type="String" name="nowReadTime">2025-07-29 14:36:49</s>
      <s type="Number" name="weekIndex">208</s>
      <s type="Number" name="doubleArmsDropTime">0</s>
      <s type="String" name="prevReadTime">2025-07-29 14:29:42</s>
      <s type="Number" name="doubleEquipDropTime">0</s>
    </s>
    <s type="Object" name="more">
      <s type="Array" name="arr">
        <s type="Object" name="null">
          <s type="Number" name="itemsLevel">1</s>
          <s type="Number" name="site">0</s>
          <s type="Boolean" name="newB">false</s>
          <s type="String" name="itemsType">more</s>
          <s type="String" name="name">WenJie</s>
          <s type="Object" name="SAVE">
            <s type="Object" name="arms">
              <s type="Array" name="arr">
                <s type="Object" name="null">
                  <s type="Number" name="capacity">6</s>
                  <s type="Object" name="critD">
                    <s type="Number" name="mul">0</s>
                    <s type="Number" name="pro">0</s>
                  </s>
                  <s type="Number" name="reloadGap">1.9955852013081312</s>
                  <s type="Number" name="penetrationNum">0</s>
                  <s type="String" name="cnName">噩梦魔王</s>
                  <s type="Array" name="l"/>
                  <s type="Boolean" name="firstChoiceB">false</s>
                  <s type="Object" name="followD">
                    <s type="Number" name="value">0</s>
                    <s type="Number" name="maxTime">0</s>
                    <s type="Boolean" name="hitIsTargetB">false</s>
                    <s type="Boolean" name="noLM">false</s>
                    <s type="Number" name="delay">0</s>
                  </s>
                  <s type="Number" name="strengthenLv">0</s>
                  <s type="Number" name="addLevel">0</s>
                  <s type="Number" name="shootAngle">15.072761702816932</s>
                  <s type="String" name="id">0103_08_998_00000000</s>
                  <s type="Object" name="o"/>
                  <s type="Number" name="bulletNum">5</s>
                  <s type="Number" name="hurtRatio">16</s>
                  <s type="Number" name="eleLv">0</s>
                  <s type="Number" name="itemsLevel">1</s>
                  <s type="Number" name="bh">0</s>
                  <s type="String" name="name">shotgun1</s>
                  <s type="Boolean" name="upgradeB">true</s>
                  <s type="Object" name="bounceD">
                    <s type="Boolean" name="noDieB">false</s>
                    <s type="String" name="shakeString"/>
                    <s type="Number" name="vMul">1</s>
                    <s type="Number" name="floor">0</s>
                    <s type="Boolean" name="glueFloorB">false</s>
                    <s type="Number" name="body">0</s>
                    <s type="Number" name="hurtNumAdd">0</s>
                    <s type="Number" name="noHitTime">0</s>
                    <s type="Boolean" name="liveInitB">false</s>
                  </s>
                  <s type="Number" name="twoShootPro">0</s>
                  <s type="String" name="itemsType">arms</s>
                  <s type="String" name="s"/>
                  <s type="Boolean" name="shopB">false</s>
                  <s type="Number" name="sMaxLv">0</s>
                  <s type="String" name="armsImgLabel">texture$m29_ak$body_shotgun3$barrel_shotgun2$grip_shotgun1$bullet_shotgun3$stock_0</s>
                  <s type="Number" name="strengthenNum">0</s>
                  <s type="Number" name="evoLv">1</s>
                  <s type="Number" name="bulletWidth">580</s>
                  <s type="String" name="shootSoundUrl">shotgun3/barrel_sound</s>
                  <s type="Boolean" name="lockB">false</s>
                  <s type="Boolean" name="newB">false</s>
                  <s type="String" name="color">white</s>
                  <s type="String" name="getTime">2025-07-29 14:21:16</s>
                  <s type="Number" name="penetrationGap">0</s>
                  <s type="Number" name="shakeAngle">0.9636270767077804</s>
                  <s type="String" name="severTime">0-01-00 00:00:00</s>
                  <s type="String" name="inHouseTime"/>
                  <s type="String" name="ele"/>
                  <s type="Number" name="attackGap">1.180773650109768</s>
                  <s type="Array" name="skillArr"/>
                  <s type="Number" name="bulletShakeWidth">48</s>
                  <s type="Number" name="site">0</s>
                  <s type="Array" name="godSkillArr"/>
                  <s type="Object" name="partsSave">
                    <s type="Array" name="arr"/>
                    <s type="Object" name="lockObj"/>
                    <s type="Number" name="lockLen">10</s>
                    <s type="Number" name="gripMaxNum">15</s>
                    <s type="Number" name="lastId">0</s>
                  </s>
                </s>
              </s>
              <s type="Object" name="lockObj"/>
              <s type="Number" name="lockLen">2</s>
              <s type="Number" name="gripMaxNum">6</s>
              <s type="Number" name="lastId">1</s>
            </s>
            <s type="Object" name="love">
              <s type="Number" name="value">700</s>
              <s type="Array" name="likeHateArr">
                <s type="Object" name="null">
                  <s type="Array" name="hateArr">
                    <s type="String" name="null">hate_7</s>
                    <s type="String" name="null">hate_3</s>
                    <s type="String" name="null">hate_2</s>
                    <s type="String" name="null">hate_1</s>
                  </s>
                  <s type="Array" name="likeArr">
                    <s type="String" name="null">like_12</s>
                    <s type="String" name="null">like_31</s>
                    <s type="String" name="null">like_15</s>
                    <s type="String" name="null">like_27</s>
                  </s>
                </s>
                <s type="Object" name="null">
                  <s type="Array" name="hateArr">
                    <s type="String" name="null">hate_2</s>
                    <s type="String" name="null">hate_8</s>
                    <s type="String" name="null">hate_6</s>
                    <s type="String" name="null">hate_4</s>
                  </s>
                  <s type="Array" name="likeArr">
                    <s type="String" name="null">like_31</s>
                    <s type="String" name="null">like_14</s>
                    <s type="String" name="null">like_33</s>
                    <s type="String" name="null">like_21</s>
                  </s>
                </s>
                <s type="Object" name="null">
                  <s type="Array" name="hateArr">
                    <s type="String" name="null">hate_3</s>
                    <s type="String" name="null">hate_7</s>
                    <s type="String" name="null">hate_8</s>
                    <s type="String" name="null">hate_11</s>
                  </s>
                  <s type="Array" name="likeArr">
                    <s type="String" name="null">like_10</s>
                    <s type="String" name="null">like_3</s>
                    <s type="String" name="null">like_18</s>
                    <s type="String" name="null">like_30</s>
                  </s>
                </s>
              </s>
              <s type="Number" name="allGivingNum">0</s>
              <s type="String" name="fashionAddTime">2025-07-29 14:21:23</s>
              <s type="String" name="likeName"/>
              <s type="Boolean" name="getGirlGiftB8">false</s>
              <s type="Number" name="dieNum">0</s>
              <s type="Number" name="buyGivingNum">0</s>
              <s type="Number" name="todayGivingNum">0</s>
              <s type="Number" name="hideNum">0</s>
              <s type="String" name="hateName"/>
              <s type="Number" name="likeGiftNum">0</s>
              <s type="Array" name="talkArr">
                <s type="String" name="null">lv_2_7</s>
                <s type="String" name="null">lv_2_3</s>
                <s type="String" name="null">lv_2_6</s>
              </s>
              <s type="Number" name="hateGiftNum">0</s>
              <s type="Number" name="allGiftNum">0</s>
              <s type="String" name="beforeGetGiftTimeStr"/>
              <s type="Boolean" name="getGirlGiftB">false</s>
              <s type="Number" name="showNum">0</s>
            </s>
            <s type="Object" name="count">
              <s type="Number" name="killNum">61</s>
              <s type="Number" name="bulletNum">0</s>
              <s type="Number" name="killBossNum">0</s>
              <s type="Number" name="dieNum">0</s>
            </s>
            <s type="Object" name="base">
              <s type="String" name="playerName">爆枪小战士</s>
              <s type="Boolean" name="lockB">false</s>
              <s type="Number" name="skillResetedNum">0</s>
              <s type="Number" name="level">10</s>
              <s type="Number" name="exp">15225</s>
              <s type="Boolean" name="playerCtrlB">false</s>
            </s>
            <s type="Object" name="equip">
              <s type="Boolean" name="showFashionB">true</s>
              <s type="Object" name="lockObj"/>
              <s type="Number" name="lockLen">10</s>
              <s type="Array" name="arr"/>
              <s type="Number" name="gripMaxNum">10</s>
              <s type="Number" name="lastId">0</s>
            </s>
            <s type="Object" name="partner">
              <s type="Object" name="ability">
                <s type="Object" name="saveObj"/>
              </s>
              <s type="Number" name="aiIndex">0</s>
              <s type="Number" name="exploit">211</s>
              <s type="Array" name="aiArr">
                <s type="Object" name="null">
                  <s type="Object" name="obj"/>
                </s>
                <s type="Object" name="null">
                  <s type="Object" name="obj"/>
                </s>
                <s type="Object" name="null">
                  <s type="Object" name="obj"/>
                </s>
              </s>
              <s type="Number" name="dayExploit">211</s>
            </s>
            <s type="Object" name="skill">
              <s type="Object" name="lockObj"/>
              <s type="Number" name="lockLen">2</s>
              <s type="Number" name="lastId">0</s>
              <s type="Array" name="arr"/>
              <s type="Number" name="gripMaxNum">10</s>
              <s type="Array" name="delNameArr"/>
            </s>
            <s type="Object" name="skillBag">
              <s type="Object" name="lockObj"/>
              <s type="Number" name="lockLen">36</s>
              <s type="Number" name="lastId">0</s>
              <s type="Array" name="arr"/>
              <s type="Number" name="gripMaxNum">36</s>
              <s type="Array" name="delNameArr"/>
            </s>
          </s>
          <s type="String" name="id">0501_08_998_00000000</s>
          <s type="String" name="cnName">文杰表哥</s>
          <s type="String" name="color">white</s>
        </s>
      </s>
      <s type="Object" name="lockObj"/>
      <s type="Number" name="lockLen">2</s>
      <s type="Number" name="gripMaxNum">6</s>
      <s type="Number" name="lastId">1</s>
    </s>
    <s type="Object" name="head">
      <s type="Object" name="obj"/>
      <s type="Number" name="nowIndex">0</s>
      <s type="String" name="nowHead"/>
    </s>
    <s type="Object" name="skillBag">
      <s type="Object" name="lockObj"/>
      <s type="Number" name="lockLen">36</s>
      <s type="Number" name="lastId">0</s>
      <s type="Array" name="arr"/>
      <s type="Number" name="gripMaxNum">36</s>
      <s type="Array" name="delNameArr"/>
    </s>
  </s>
  <ignoreWhitespace>true</ignoreWhitespace>
</saveXml>